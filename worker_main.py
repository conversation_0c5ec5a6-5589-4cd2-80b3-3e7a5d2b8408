import sys
import os

sys.path.append('./mock')
from plugin import BorePyTorchPlugin
from sdk import JobParameter, ExecutorProvider

if __name__ == '__main__':
    execution_provider = ExecutorProvider()
    plugin = BorePyTorchPlugin(execution_provider)
    model = {}
    [model.setdefault(k, v) for k, v in [
        ('f_app_group_id', os.getenv('ENV_APP_GROUP_ID')),
        ('f_session_id', ''),
        ('appinstance_name', ''),
        ('opset_version', int(os.getenv('ENV_OPSET_VERSION', '13'))),
        ('f_job_id', '-1'),
        ('job_owner', os.getenv('ENV_OPERATOR')),
        ('op_code', 'ConvertWorker'),
        ('op_version', 'v0.1.0'),
        ('env', 'formal'),
        ('is_debug', 'False'),
        ('modelName', os.getenv('ENV_MODEL_NAME')),
        ('modelVersion', os.getenv('ENV_MODEL_VERSION')),
        ('outputModelName', os.getenv('ENV_OUTPUT_MODEL_NAME')),
        ('is_register', True),
        ('keepVersion', '5'),
        ('predictTarget', os.getenv('ENV_PREDICT_TARGET')),
        ('placeholderReplace', os.getenv('ENV_CPU_TARGET')),
        ('batchSize', os.getenv('ENV_BATCH_SIZE')),
        ('engine_type', os.getenv('ENV_ENGINE_TYPE')),
        ('fp16', os.getenv('ENV_USE_FP16', 'False').lower() in ('true', '1')),
        ('use_refit', os.getenv('ENV_BUILD_REFITTABLE_ENGINE', 'True').lower() in ('true', '1')),
        ('convert_with_refit', os.getenv('ENV_CONVERT_WITH_REFIT', 'False').lower() in ('true', '1')),
        ('force_convert_with_refit', os.getenv('ENV_FORCE_CONVERT_WITH_REFIT', 'False').lower() in ('true', '1')),
        ('use_evart_tool', os.getenv('ENV_USE_EVART_TOOL', 'False').lower() in ('true', '1')),
        ('paramsPairs', ''),
        ('user_inputs', os.getenv('ENV_USER_INPUTS')),
        ('item_inputs', os.getenv('ENV_ITEM_INPUTS')),
        ('boreConfig.resourceGroupName', 'resource_group_name'),
        ('proxy_user_secret_id', os.getenv('ENV_SECRET_ID')),
        ('proxy_user_secret_key', os.getenv('ENV_SECRET_KEY')),
        ('task_id', os.getenv('ENV_TASK_ID')),
    ]]
    job_parameter = JobParameter(model)
    job_parameter.secret = {
        'venus_openapi_sid': model['proxy_user_secret_id'],
        'venus_openapi_skey': model['proxy_user_secret_key'],
    }
    plugin.execute(job_parameter)
