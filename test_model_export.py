import logging
import os
import sys

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '0'

from model_export import param_parser
from graph_converter import graph_converter
from trt import trt_engine
from evart import evart_engine, make_config_json


def convert_model(engine_type: str, model_path: str, batchs: list, opset_version: int, predict_target: list,
                  placeholder_replace: list = None):
    parameter_parser = param_parser.NumerousDenseParameterParser(model_path)
    parameter_parser.parse_slice()
    parameter_parser.parse_dense_parameters_conf()
    parameter_parser.read_dense_parameters()

    converter = convert_graph.TFGraphConverter(parameter_parser, predict_target, placeholder_replace)
    converter.clean_and_freeze_graph(model_path)
    converter.split_cpu_targets(model_path)

    is_fp16 = False
    use_refit = True
    if engine_type == 'trt':
        for batch_size in batchs:
            batch_onnx_path = os.path.join(model_path, 'model_%d.onnx' % batch_size)
            converter.export_to_onnx(batch_size, batch_onnx_path, engine_type, opset_version, False)
            output_engine_file = os.path.join(model_path, 'model_%d.onnx.engine' % batch_size)
            trt_engine.build_trt_engine(batch_onnx_path, is_fp16, use_refit, output_engine_file)
    else:
        batch_size = batchs[0]
        batch_onnx_path = os.path.join(model_path, 'model.onnx')
        converter.export_to_onnx(batch_size, batch_onnx_path, engine_type, opset_version, False)
        eva_config_file = os.path.abspath(os.path.join(model_path, "evart_config.json"))
        make_config_json.write_config_file(eva_config_file, batch_onnx_path, is_fp16, batchs, engine_type,
                                           opset_version)
        output_engine_file = os.path.join(model_path, 'model.evart')
        test_inference = True
        evart_engine.build_evart_engine(batch_onnx_path, output_engine_file, eva_config_file, batchs, is_fp16,
                                        test_inference)


if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )
    # input argv
    engine_type = 'evart'
    model_path = 'models/2022_08_09_150423'
    batchs_str = '100;300;1000'
    opset_version = 13
    predict_target_str = 'cmpl_regression_old/cmpl_regression_old;finish_old;skip_old;positive_old'
    placeholder_replace_str = 'emb_concat_with_product;lr_input_concat'

    # initialize
    batchs = [int(batch) for batch in batchs_str.split(';')]
    predict_target = [output for output in predict_target_str.split(';')]
    placeholder_replace = [target for target in placeholder_replace_str.split(';')]
    if len(placeholder_replace_str) == 0:
        placeholder_replace = None

    # function
    convert_model(engine_type, model_path, batchs, opset_version, predict_target, placeholder_replace)
