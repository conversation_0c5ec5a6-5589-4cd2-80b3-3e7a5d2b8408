from utils.package_checker import is_package_installed


def get_device_type():
    if not is_package_installed('pynvml'):
        return 'unknown'

    import pynvml
    # 获取显卡类型
    pynvml.nvmlInit()
    gpu_id = 0
    handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_id)
    gpu_device_info = pynvml.nvmlDeviceGetName(handle)
    pynvml.nvmlShutdown()

    try:
        device_info = gpu_device_info.decode()
    except Exception as e:
        device_info = gpu_device_info

    return device_info


def get_cuda_version():
    if not is_package_installed('pycuda'):
        return '0.0.0'

    import pycuda.driver as cuda
    cuda.init()
    version = cuda.get_version()
    return '.'.join(str(i) for i in version)
