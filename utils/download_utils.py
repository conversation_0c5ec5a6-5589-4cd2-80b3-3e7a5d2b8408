import logging
import subprocess
import time
import os

def download_with_retry(download_command, file_path, expected_sha1=None, sha1_calculator=None, max_retries=3, retry_interval=2):
    """
    执行下载命令并进行重试，可选择验证下载文件的SHA1值
    
    参数:
        download_command: 下载命令字符串
        file_path: 下载文件的保存路径
        expected_sha1: 期望的SHA1值，如果为None则不验证SHA1
        sha1_calculator: 计算SHA1的函数，接收文件路径参数，返回SHA1字符串
        max_retries: 最大重试次数
        retry_interval: 重试间隔时间(秒)
        
    返回:
        bool: 下载是否成功
    """
    retry_count = 0
    download_success = False
    
    while retry_count < max_retries and not download_success:
        try:
            # 执行下载命令
            result = subprocess.run(download_command, shell=True, check=True)
            
            # 如果需要验证SHA1
            if expected_sha1 and sha1_calculator:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    logging.warning(f"Downloaded file not found at {file_path}. Retry {retry_count+1}/{max_retries}")
                    retry_count += 1
                    time.sleep(retry_interval)
                    continue
                    
                # 检查下载后的文件SHA1是否正确
                actual_sha1 = sha1_calculator(file_path)
                if actual_sha1 == expected_sha1:
                    logging.info(f'File downloaded successfully: {file_path}')
                    download_success = True
                else:
                    retry_count += 1
                    logging.warning(f'Downloaded file has incorrect SHA1 checksum. Expected: {expected_sha1}, Got: {actual_sha1}. Retry {retry_count}/{max_retries}')
                    time.sleep(retry_interval)
            else:
                # 如果不需要验证SHA1，只要命令执行成功就认为下载成功
                logging.info(f'File downloaded successfully: {file_path}')
                download_success = True
                
        except subprocess.CalledProcessError as e:
            retry_count += 1
            logging.warning(f'Download failed with error: {str(e)}. Retry {retry_count}/{max_retries}')
            time.sleep(retry_interval)
    
    if not download_success:
        logging.error(f'Failed to download file to {file_path} after {max_retries} retries.')
    
    return download_success
