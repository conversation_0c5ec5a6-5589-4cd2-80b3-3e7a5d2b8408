import sys
import time
import logging
from typing import Callable

from components import const


def with_retry(retry_count: int, interval: float, backoff: float = 1.0):
    """
    装饰器：对于抛出异常的函数进行重试
    :param retry_count: 重试次数
    :param interval: 重试间隔时间(秒)
    :param backoff: 退避机制，等待时间指数递增，默认为1(间隔时间不变)，等待时间间隔最大限制为 1 分钟
    """

    def with_retry_wrapper(func: Callable):

        def func_wrapper(*args, **kwargs):
            for i in range(retry_count):
                try:
                    ret = func(*args, **kwargs)
                    return ret
                except:  # pylint: disable=bare-except
                    _, exc, _ = sys.exc_info()
                    if i == retry_count - 1:
                        logging.error('Run %s fail after retry %d times, abort' % (func.__name__, retry_count))
                        raise exc
                    else:
                        logging.error('Run %s fail with retry count %d, exception: %s' % (func.__name__, i, str(exc)))
                        sleep_time = min(interval * (backoff ** i), const.MAX_RETRY_INTERVAL)
                        logging.info('Sleep %.1f seconds...' % sleep_time)
                        time.sleep(sleep_time)
            return None

        return func_wrapper

    return with_retry_wrapper
