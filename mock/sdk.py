# -*- coding: utf-8 -*-
#
# Copyright @ 2020 Tencent.com
"""
组建SDK模块，提供给开发者和内部资源调度继承使用.
该模块定义了如下class：
- `JobParameter`，自定义组件运行时需要的所有参数集合，是平台用户对作业的配置，
   开放给开发者
- `ExecutorParameter`，调度工作（e.g. <PERSON><PERSON>, TensorFlow）需要的所有参数集合
  是BaseOperator类对底层执行环境的配置，由Executor方调用
- `ExecutorProvider`, container 环境下调起其他 container 工作的操作接口
- `Executor`，调度工作的发起者
- `BaseOperator`，提供给开发者继承使用，用于实现自定义组件的接口类
如何使用这个模块
================
1. import
---------
``from framework.driver import ...``. e.g.
``from framework.driver import MasterBoreDriver``
2. 创建一个调度接口实例
-----------------------
``masterBoreDriver: MasterBoreDriver = MasterBoreDriver()``
3. 调度工作
-----------
``masterBoreDriver.execute(parameter, executor_provider)``
"""


import copy


class JobParameter:
    """
    是自定义组件运行时需要的所有参数集合，是平台用户对作业的配置，
    开放给开发者
    Methods:
        __init__(self, parameters: dict)
        __setitem__(self, key: str, value: str) -> None
        _getitem__(self, key: str) -> str
        __contains__(self, key: str) -> bool
    """
    def __init__(self, parameters: dict):
        """
        构造器.
        :param parameters:  预设的参数键值对映射
        :type parameters: dict
        """
        self.dict = copy.deepcopy(parameters)

    def __setitem__(self, key, value):
        """
        建立一个参数键到某个参数值的映射.
        :param key:  参数键
        :type key: str
        :param value:  参数键对应的参数值
        :type value: str
        """
        self.dict[key] = value

    def __getitem__(self, key):
        """
        得到参数中和某个参数键对应的参数值.
        :param key:  参数键
        :type key: str
        :return:  参数键对应的一个参数值
        :rtype: str
        """
        return self.dict[key]

    def __contains__(self, key: object) -> bool:
        """
        检查某一个参数键是否对应了一个参数值.
        :param key:  参数键
        :type key: str
        :return:  如果参数键
        :rtype: bool
        """
        return key in self.dict


class ExecutorParameter:
    """
    调度工作（e.g. Bore, TensorFlow）需要的所有参数集合
    Methods:
        __init__(self, parameters: dict)
        __setitem__(self, key: str, value: str) -> None
        _getitem__(self, key: str) -> str
        __contains__(self, key: str) -> bool
    """
    def __init__(self, parameters: dict):
        """
        构造器.
        :param parameters:  预设的参数键值对映射
        :type parameters: dict
        """
        self.dict = copy.deepcopy(parameters)

    def __setitem__(self, key: str, value: str) -> None:
        """
        建立一个参数键到某个参数值的映射.
        :param key:  参数键
        :type key: str
        :param value:  参数键对应的参数值
        :type value: str
        """
        self.dict[key] = value

    def __getitem__(self, key: str) -> str:
        """
        得到参数中和某个参数键对应的参数值.
        :param key:  参数键
        :type key: str
        :return:  参数键对应的一个参数值
        :rtype: str
        """
        return self.dict[key]

    def __contains__(self, key: str) -> bool:
        """
        检查某一个参数键是否对应了一个参数值.
        :param key:  参数键
        :type key: str
        :return:  如果参数键
        :rtype: bool
        """
        return key in self.dict


class Executor:
    """
    获取自定义组件参数，调起后台资源（e.g. Bore, TensorFlow jobs），并且将
    组件参数传给组件运行时使用
    Methods:
    get_name(self) -> str
    submit_task(self, executor_parameter: ExecutorParameter, executor_provider) -> object:
    """
    def get_name(self) -> str:
        """
        获取这个执行器的名称用于容器环境下不同容器执行工作的区分.
        :return: 这个执行器的名称
        :rtype: str
        """
        raise NotImplementedError

    def submit_task(self, executor_parameter: ExecutorParameter, executor_provider) -> object:
        """
        给framework调度模块提交一个"启动作业"的调度命令.
        :param executor_parameter:  包含开启一个作业需要的所有调度参数的一个
        object
        :type executor_parameter: ExecutorParameter
        :param executor_provider:  在一个作业容器中调起定一个容器作业的操作端口
        :type executor_provider: ExecutorProvider
        :return: framework调度开启之后从远程被调度方（比如Bore, Tensorflow）
        传回的调度请求结果
        :rtype: object
        """
        raise NotImplementedError


class ExecutorProvider:
    """
    容器环境下调起其他容器工作的接口
    Methods:
        add_executor(self, executor: Executor) -> None
        get_task_excecutor(self, executor_name: str) -> Executor
    """
    def add_executor(self, executor: Executor) -> None:
        """
        添加一个容器环境下开启其他容器作业对应的作业执行器
        :param executor:  一个自定义执行器
        :type executor: Executor
        """
        raise NotImplementedError

    def get_task_excecutor(self, executor_name: str) -> Executor:
        """
        得到一个指定名称的执行器.
        :param executor_name:  一个自定义执行器名称
        :type executor_name: str
        :return: 一个名字和指定名字相同的执行器
        :rtype: Executor
        """
        raise NotImplementedError


class BaseOperator:
    """
    提供给开发者继承使用，用于实现自定义组件的接口类
    Methods
        __init__(self, executor_provider: ExecutorProvider)
        pre_config(self, job_parameter: JobParameter) -> None
        post_config(self, job_parameter: JobParameter) -> None
        pre_execute(self, job_parameter: JobParameter) -> None
        execute(self, job_parameter: JobParameter) -> object
        post_execute(self, job_parameter: JobParameter) -> None
        validate(self, job_parameter: JobParameter) -> bool
        get_status(self, job_parameter: JobParameter) -> object
        kill(self, job_parameter: JobParameter) -> bool
    """
    def __init__(self, executor_provider: ExecutorProvider):
        """
        构造器
        :param executor_provider:  在一个作业容器中调起定一个容器作业的操作端口
        :type executor_provider: ExecutorProvider
        """
        self.executor_provider = executor_provider

    def pre_config(self, job_parameter: JobParameter) -> None:
        """
        执行自定义组件预配置，可以不实现
        :param job_parameter:  包含预配置所需的所有组件参数的一个object
        :type job_parameter: JobParameter
        """

    def post_config(self, job_parameter: JobParameter) -> None:
        """
        执行自定义组件在后台准备完毕之后的后期配置，可以不实现
        :param job_parameter:  包含后期配置所需的所有组件参数的一个object
        :type job_parameter: JobParameter
        """

    def pre_execute(self, job_parameter: JobParameter) -> None:
        """
        执行自定义组件的预执行逻辑，可以不实现
        :param job_parameter:  包含预执行所需的所有组件参数的一个object
        :type job_parameter: JobParameter
        """

    def execute(self, job_parameter: JobParameter) -> object:
        """
        开启一个作业
        :param job_parameter:  包含开启一个作业需要的所有调度参数的一个object
        :type job_parameter: JobParameter
        :return: 开启作业之后回传给调度发起者的相应信息，比如 HTTP 相应 JSON
        :rtype: object
        """
        raise NotImplementedError

    def post_execute(self, job_parameter: JobParameter) -> None:
        """
        执行自定义组件的后期执行逻辑，可以不实现
        :param job_parameter:  包含后期执行所需的所有组件参数的一个object
        :type job_parameter: JobParameter
        """

    def validate(self, job_parameter: JobParameter) -> bool:
        """
        校验作业调度需要的参数，比如所有参数时候都包含，参数类型是否正确等等.
        :param job_parameter:  所有要被校验的参数集合
        :type job_parameter: JobParameter
        :return: 如果校验通过，返回 true ，否则返回 false
        :rtype: bool
        """
        raise NotImplementedError

    def get_status(self, job_parameter: JobParameter) -> object:
        """
        在某个时间点得到一个作业的运行状态，比如SUCCESS, FAILED.
        :param parameter:  包含了监听作业状态需要的调度参数的一个object
        :type parameter: ExecutorParameter
        :return: 表示作业状态的一个object
        :rtype: object
        """
        raise NotImplementedError

    def kill(self, job_parameter: JobParameter) -> bool:
        """
        终止一个调度作业.
        :param job_parameter:  包含了停止作业需要的调度参数的一个object
        :type job_parameter: JobParameter
        :return: 如果成功停止，返回True，否则返回False
        :rtype: bool
        """
        raise NotImplementedError
