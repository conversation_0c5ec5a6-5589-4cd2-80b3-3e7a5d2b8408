#!/data/anaconda3/bin/python3
# -*- coding: UTF-8 -*-
"""
组建bore master runner模块，用于启动开发者插件代码并提供插件所需的参数.

该模块定义了如下class：

- `BoreMasterRunner`，bore 类型任务在容器内的启动类
"""


import getopt
import sys
import os
import json
import importlib


sys.path.insert(0, os.path.abspath('../'))
sys.path.insert(0, os.path.abspath('./'))


class BoreMasterRunner:
    """
    这个是在applicationmaster上运行的,bore 类型任务在容器内的启动脚本

    Methods:
        __init__(self, argv: list)
        startup_op_bussines(self) ->None
        parse_param(self, argv: list) ->dict
        __contains__(self, key: str) -> bool
    """

    def __init__(self, argv: list):
        """
        构造器

        :param argv: 外部传入参数
        :type argv: list
        """
        # 动态设置spark的lib path，并且动态加载类模块
        self.parse_argv = self.parse_param(argv)
        self.parameters = self.parse_param_by_json_str(self.get_data())
        # 插件代码需要强制设定BoreTFPlugin类别
        self.op_module = importlib.import_module("plugin")
        self.task_executor_provider = self.op_module.TaskExecutorProvider()
        self.op = self.op_module.BorePyTorchPlugin(self.task_executor_provider)
        self.task_executor = self.op_module.TaskExecutor()
        self.task_job_parameter = self.op_module.TaskJobParameter(self.parameters)

    def startup_op_bussines(self) -> None:
        """
        op启动方法
        """
        self.op.execute(self.task_job_parameter)

    def parse_param(self, argv: list) -> dict:
        """
        参数解析

        :param argv: 命令行参数
        :type argv: list
        :return: 解析后的参数
        :rtype: dict
        """
        parse_argv = {}
        try:
            opts, args = getopt.getopt(argv, "d:", ["data="])
        except getopt.GetoptError:
            return {}
        for opt, arg in opts:
            if opt in ("-d", "--data"):
                job_config_json = arg
                parse_argv["data"] = job_config_json
        return parse_argv

    def get_data(self) -> str:
        """
        获取data的值

        :return: data的值
        :rtype: str
        """
        data = ""
        if "data" in self.parse_argv:
            data = self.parse_argv.get("data")
        return data

    def parse_param_by_json_str(self, data_str) -> dict:
        """
        解析json文件

        :return: 作业配置参数
        :rtype: dict
        """
        parameters = {}
        parameters = json.loads(data_str)
        return parameters

    def validate(self) -> None:
        """
        验证debug命令传入的参数是否为空

        """
        if len(self.parameters) == 0:
            print("parameter data error, exit!")
            sys.exit(1)


if __name__ == "__main__":
    bore_runner = BoreMasterRunner(sys.argv[1:])
    bore_runner.validate()
    bore_runner.startup_op_bussines()
