import logging
import os
import sys
import re
import shutil
import subprocess

from components.const import PYTHON_RUN

def _get_env():
    env = os.environ.copy()
    env['PYTHONPATH'] = '%s:%s' % \
                        (os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), env.get('PYTHONPATH', ''))
    return env


def validate_evart_engine(onnx_file: str, batch: int):
    # TODO:该进程失败，转模任务结束
    # 目前该进程失败对后续无影响
    script = os.path.abspath(os.path.join(os.path.dirname(__file__), 'validate_evart_engine.py'))
    subprocess.run('source /etc/profile && "%s" "%s" "%s" "%d"' %
                   (PYTHON_RUN, script, onnx_file, batch),
                   shell=True, env=_get_env())

def build_evart_engine(onnx_file: str, engine_file: str, config_file: str, batches: list, fp16: bool, test_inference: bool = False):

    logging.info('Build evart engine %s from %s...', engine_file, onnx_file)

    if fp16:
        logging.info('Build evart engine use fp16', engine_file)

    # -U0 means not refit
    use_refit = False

    # cmd_path = './evart/evartConverterTool'
    script = os.path.join(os.path.dirname(__file__), 'evartConverterTool')
    command = 'source /etc/profile && "%s" "-U%d" "-c%s"' % \
              (script, use_refit, config_file)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print evart subprocess log to stdout
    evart_log = p.stdout.decode('utf-8')
    print(evart_log)

    # check subprocess error
    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))

    # validate inference result and parse log
    if test_inference:
        validate_evart_engine(onnx_file, batches[0])
    logging.info('Build evart engine %s done...', engine_file)

# TODO: Need to optimize the refit implementation of evartConverterTool
def update_evart_engine(original_engine_file:str, onnx_file: str, engine_file: str, config_file: str, batches: list,
                        fp16: bool, test_inference: bool = False):
    logging.info('Update evart engine %s from %s...', original_engine_file, onnx_file)

    # -U1 means refit
    use_refit = True

    # cmd_path = './evart/evartConverterTool'
    script = os.path.join(os.path.dirname(__file__), 'evartConverterTool')
    command = 'source /etc/profile && "%s" "-U%d" "-m%s" "-c%s"' % \
              (script, use_refit, original_engine_file, config_file)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print evart subprocess log to stdout
    evart_log = p.stdout.decode('utf-8')
    print(evart_log)

    # check subprocess error
    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))

    # validate inference result and parse log
    if test_inference:
        validate_evart_engine(onnx_file, batches[0])
    logging.info('Update evart engine %s done...', engine_file)

if __name__ == '__main__':
    logging.basicConfig(
      format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
      level=logging.INFO, stream=sys.stdout, force=True
    )

    onnx_file = sys.argv[1]
    engine_file = sys.argv[2]
    config_file = 'evart_config.json'
    batches = [int(batch) for batch in sys.argv[3].split(';')]
    fp16 = int(sys.argv[4])
    test_inference = False

    build_evart_engine(onnx_file, engine_file, config_file, batches, fp16, test_inference)

