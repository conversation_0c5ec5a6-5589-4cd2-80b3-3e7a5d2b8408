# evart

## mirror
```
docker run -itd --name numerous_tensorrt --shm-size="128g" --gpus all --net=host \
  --privileged -v /local/path/:/workspace/ mirrors.tencent.com/todacc/venus-c-bowenxiao-cuda10.2-trt8.2:0.1.6 /bin/bash
docker exec -it numerous_tensorrt /bin/bash
conda activate env-3.8.8
alias vi=vim
```
## download
```
wget https://mirrors.tencent.com/repository/generic/evaonnx/evart_script.tar.gz
tar pzxvf evart_script.tar.gz /local/evart/install/
```
## environment
set environment for evartEngineTest and evartConverterTool execution
```
# the path where evart is installed
export EVART_ROOT_PATH=/local/evart/install
export LD_LIBRARY_PATH=${EVART_ROOT_PATH}/lib/:$LD_LIBRARY_PATH
# wget https://developer.download.nvidia.com/compute/cutensor/redist/libcutensor/linux-x86_64/libcutensor-linux-x86_64-*******-archive.tar.xz
export LD_LIBRARY_PATH=/libcutensor-linux-x86_64-*******-archive/lib/10.2/:$LD_LIBRARY_PATH
# /data/miniconda3/lib/libstdc++.so.6.0.26
export LD_LIBRARY_PATH=/data/miniconda3/lib/:$LD_LIBRARY_PATH
export PATH=${EVART_ROOT_PATH}/converter/tool/:${EVART_ROOT_PATH}/test/:$PATH
```
verify environment
```
# evartEngineTest
[PARAMETERS INFO]:
<<<<<<<<<<<<<<<<<<<< Parameters specification for Engine Test >>>>>>>>>>>>>>>>>>>>
--modelFile--  or -m:  --required-- specific file to load evart model
--batch--      or -b:  --optional-- specific evart model batch, the default batch is the max batch val of model
--ioData--     or -d:  --optional-- specific input/output data to verify result, currently support numerous data format
--ioSetApi--   or -s:  --optional-- specific set input & get output api data type: 0(cpu data) 1(cpu merged data) ,default 0
--thread--     or -t:  --optional-- specific thread numbers to run, each thread will binding with an individual engine and run the task in parallel ,default 1
--warmUp--     or -w:  --optional-- specific engine runing warm up times ,default 100
--loop--       or -l:  --optional-- specific engine runing loop up times ,default 20
# evartConverterTool
[PARAMETERS INFO]:
<<<<<<<<<<<<<<<<<<<< Parameters specification for Converter >>>>>>>>>>>>>>>>>>>>
--configFile         or -c:  --required-- specific config json file
--useUpdateMode      or -U:  --optional-- true or false, use new onnx model to update evart model weight
--updateModelFile    or -m:  --optional-- specific original evart model file, required when use update mode
```


