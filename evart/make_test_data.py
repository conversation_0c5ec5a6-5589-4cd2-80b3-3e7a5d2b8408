import logging
import os
import sys

import numpy as np
import onnx
import tensorflow as tf

from components.const import FROZEN_GRAPH_FILENAME

def make_data_from_tf(onnx_file: str):
    onnx_model = onnx.load_model(onnx_file)

    # generate input data
    input_data = dict()
    for input in onnx_model.graph.input:
        shape = [dim.dim_value for dim in input.type.tensor_type.shape.dim]
        input_array = np.random.randn(*shape).astype(np.float32)
        input_data[input.name] = input_array

    output_keys = [output.name for output in onnx_model.graph.output]

    # load graph
    with open(os.path.join(os.path.dirname(onnx_file), FROZEN_GRAPH_FILENAME), 'rb') as f:
        graph_def = tf.compat.v1.GraphDef()
        graph_def.ParseFromString(f.read())

    # generate output data
    output_data = dict()
    tf_graph = tf.Graph()
    with tf_graph.as_default():
        tf.compat.v1.import_graph_def(graph_def=graph_def, name='')
        with tf.compat.v1.Session(graph=tf_graph) as sess:
            output_tensors = [tf_graph.get_tensor_by_name(k) for k in output_keys]
            feed_dict = {tf_graph.get_tensor_by_name(k): v for k, v in input_data.items()}
            tf_outputs = sess.run(output_tensors, feed_dict)
            for output_name, output_array in zip(output_keys, tf_outputs):
                output_data[output_name] = output_array

    return input_data, output_data

def write_array(f, array):
    # write shape
    if len(array.shape) == 0:
        f.write('0\n')
    else:
        f.write('%d %s\n' % (len(array.shape), ' '.join([str(dim) for dim in array.shape])))

    # write value
    f.write('%s\n' % ' '.join([str(val) for val in array.flat]))

def write_data(inputs, outputs, filename):
    with open(filename, 'w') as f:
        f.write('*** inputs begin ***\n')
        f.write('%d\n' % len(inputs))
        for input_name, v in inputs.items():
            if (input_name[-2:] == ":0"):
                f.write('%s\n' % input_name[:-2])
            else:
                f.write('%s\n' % input_name)
            write_array(f, v)

        f.write('*** preds begin ***\n')
        f.write('%d\n' % len(outputs))
        output_names = sorted(outputs.keys())
        for output_name in output_names:
            if (output_name[-2:] == ":0"):
                f.write('%s\n' % output_name[:-2])
            else:
                f.write('%s\n' % output_name)

        f.write('*** outputs begin ***\n')
        f.write('%d\n' % len(outputs))
        for output_name in output_names:
            v = outputs[output_name]
            write_array(f, v)

def read_array(f):
    shape = [int(v) for v in f.readline().strip().split()]
    value = [float(v) for v in f.readline().strip().split()]
    shape_count = shape[0]
    if shape_count == 0:
        array = np.array(value[0]).astype(np.float32)
    else:
        tensor_shape = shape[1:]
        assert len(tensor_shape) == shape_count
        array = np.array(value).reshape(tensor_shape).astype(np.float32)
    return array


def read_data(filename):
    inputs = dict()
    outputs = dict()

    with open(filename, 'r') as f:
        dummy = f.readline().strip()
        assert dummy == '*** inputs begin ***'

        inputs_num = int(f.readline().strip())
        for _ in range(inputs_num):
            name = f.readline().strip()
            array = read_array(f)
            inputs[name] = array
            print(name)

        dummy = f.readline().strip()
        if dummy != '*** preds begin ***':
            print('No outputs data, stop parsing...')
            return inputs, outputs

        output_num = int(f.readline().strip())
        output_names = list()
        for _ in range(output_num):
            output_names.append(f.readline().strip())

        dummy = f.readline().strip()
        assert dummy == '*** outputs begin ***'
        output_num = int(f.readline().strip())
        output_arrays = list()
        for _ in range(output_num):
            array = read_array(f)
            output_arrays.append(array)

        for k, v in zip(output_names, output_arrays):
            outputs[k] = v

    return inputs, outputs

