import logging
import os
import sys
import subprocess

import onnx

import make_test_data

def _get_env():
    env = os.environ.copy()
    env['PYTHONPATH'] = '%s:%s' % \
                        (os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), env.get('PYTHONPATH', ''))
    return env

def test_evart_inference(engine_file: str, batch: int, data_file: str):

    # cmd_path = "./evart/evartEngineTest"
    script = os.path.join(os.path.dirname(__file__), 'evartEngineTest')
    warmup_times = 0
    run_times = 1
    streams = 1
    command = '"%s" "-m%s" "-b%d" -d%s -w%d -l%d -t%d' % \
              (script, engine_file, batch, data_file, warmup_times, run_times, streams)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print evart subprocess log to stdout
    evart_log = p.stdout.decode('utf-8')
    print(evart_log)


def validate_evart_engine(onnx_file: str, batch: int):
    logging.info('Start to validate EvaRT inference result...')
    logging.info('Make test data from TFSessionRun...')
    input_data, output_data = make_test_data.make_data_from_tf(onnx_file)

    # write input_data and output_data to dat file 
    data_file = os.path.abspath(os.path.join(os.path.dirname(onnx_file), "data_" + str(batch) + ".dat"))
    logging.info('Write test data to %s...', data_file)
    make_test_data.write_data(input_data, output_data, data_file)

    engine_file = os.path.abspath(onnx_file.rstrip("onnx") + "evart")
    logging.info('Run EvaRT inference %s and compare result with tensorflow...', engine_file)
    test_evart_inference(engine_file, batch, data_file)



if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    onnx_file = sys.argv[1]
    batch = int(sys.argv[2])
    validate_evart_engine(onnx_file, batch)
