import logging
import os
import sys

import numpy as np
import onnx
import tensorflow as tf

from trt.trt_inference import TensorRTInference

def infer_trt_engine(engine_file_list: list):
    logging.info('Start to check TensorRT output sort...')

    output_list = list()
    for engine_file in engine_file_list:
        trt_inference = TensorRTInference(engine_file)
        trt_inference.load()
        output = trt_inference.infer_output()
        output_list.append(output)

    for output in output_list:
        count = 0
        if output == output_list[0]:
            logging.info('engine校验通过 %s' % output)
            count += 1
        else:
            logging.info('engine校验失败 %s' % output)
            raise Exception('TensorRT check engine fail')


if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )
    engine_file_str = sys.argv[1]
    engine_file = [output for output in engine_file_str.split(';')]
    infer_trt_engine(engine_file)
