import logging
import os
import re
import shutil
import subprocess

from components.const import WEIGHT_TRANSPOSE_FILENAME, PYTHON_RUN, EVART_CONVERTER_SCRIPT

PATTERN = re.compile('Weights (.*?) has been transposed with permutation of (.*?)!')


def _get_env():
    env = os.environ.copy()
    env['PYTHONPATH'] = '%s:%s' % \
                        (os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), env.get('PYTHONPATH', ''))
    return env

def refit_trt_engine(original_engine_file: str, original_onnx_file: str, new_onnx_file: str, output_engine_file: str, first_run=False):
    script = os.path.abspath(os.path.join(os.path.dirname(__file__), 'refit_trt_engine.py'))
    subprocess.run('source /etc/profile && "%s" "%s" "%s" "%s" "%s" "%s"' %
                   (PYTHON_RUN, script, original_engine_file, original_onnx_file, new_onnx_file, output_engine_file),
                   shell=True, check=True, env=_get_env())

    if first_run:
        # validate inference result
        validate_trt_engine(output_engine_file, new_onnx_file)

def validate_trt_engine(engine_file: str, onnx_file: str):
    # 该进程失败对后续无影响
    script = os.path.abspath(os.path.join(os.path.dirname(__file__), 'validate_trt_engine.py'))
    subprocess.run('source /etc/profile && "%s" "%s" "%s" "%s"' %
                   (PYTHON_RUN, script, engine_file, onnx_file),
                   shell=True, env=_get_env())


def infer_trt_engine(engine_file_list: list):
    engine_file = ';'.join(engine_file_list)
    script = os.path.abspath(os.path.join(os.path.dirname(__file__), 'check_trt_engine.py'))
    command = 'source /etc/profile && "%s" "%s" "%s"' % \
              (PYTHON_RUN, script, engine_file)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())
    trt_log = p.stdout.decode('utf-8')
    print(trt_log)

    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))


def build_trt_engine_by_plugin(onnx_file: str, fp16: bool, use_refit: bool, output_engine_file: str,
                               user_item_param: str, first_run=False):
    script = os.path.abspath(os.path.join(os.path.dirname(__file__), 'build_trt_engine.py'))
    fp16_str = str(int(fp16))
    refit_str = str(int(use_refit))
    command = f"source /etc/profile && '{PYTHON_RUN}' '{script}' '{onnx_file}' '{fp16_str}' '{refit_str}' '{output_engine_file}' '{user_item_param}'"
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print trt subprocess log to stdout
    trt_log = p.stdout.decode('utf-8')
    print(trt_log)

    # check subprocess error
    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))

    if first_run:
        # validate inference result and parse log
        validate_trt_engine(output_engine_file, onnx_file)

        match = PATTERN.findall(trt_log)
        output_transpose_file = os.path.join(os.path.dirname(output_engine_file), WEIGHT_TRANSPOSE_FILENAME)
        logging.info('Writing weight transpose file to %s...' % output_transpose_file)
        with open(output_transpose_file, 'w') as f:
            for name, perm in match:
                logging.info('Weight name: %s, perm: %s' % (name, perm))
                f.write('%s|%s\n' % (name, perm))


def build_trt_engine(onnx_file: str, engine_file: str, config_file: str, batches: list, test_inference: bool = False):
    logging.info('Building TensorRT engine %s from ONNX file %s...' % (engine_file, onnx_file))

    # cmd_path = './evart/evartConverterTool'
    script = EVART_CONVERTER_SCRIPT
    use_update_mode = 0
    command = 'source /etc/profile && "%s" "-U%d" "-c%s"' % (script, use_update_mode, config_file)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print evartConverterTool subprocess log to stdout
    trt_log = p.stdout.decode('utf-8')
    print(trt_log)

    # check subprocess error
    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))

    logging.info('Build TensorRT engine %s done...', engine_file)

    if test_inference:
        output_engine_file = os.path.join(os.path.dirname(onnx_file), 'model_%d.engine' % batches[0])
        logging.info('Validate trt engine %s vs ONNX file %s...' % (output_engine_file, onnx_file))
        validate_trt_engine(output_engine_file, onnx_file)

        match = PATTERN.findall(trt_log)
        output_transpose_file = os.path.join(os.path.dirname(output_engine_file), WEIGHT_TRANSPOSE_FILENAME)
        logging.info('Writing weight transpose file to %s...' % output_transpose_file)
        with open(output_transpose_file, 'w') as f:
            for name, perm in match:
                logging.info('Weight name: %s, perm: %s' % (name, perm))
                f.write('%s|%s\n' % (name, perm))
