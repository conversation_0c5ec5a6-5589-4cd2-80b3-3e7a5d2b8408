import logging
import os
import sys

import numpy as np
import onnx
import tensorflow as tf

from google.protobuf import text_format

from components.const import FROZEN_GRAPH_FILENAME
from trt.trt_inference import TensorRTInference


def make_test_data(onnx_file: str):
    onnx_model = onnx.load_model(onnx_file)

    # generate input data
    input_data = dict()
    for input in onnx_model.graph.input:
        shape = [dim.dim_value for dim in input.type.tensor_type.shape.dim]
        input_array = np.random.randn(*shape).astype(np.float32)
        input_data[input.name] = input_array

    output_keys = [output.name for output in onnx_model.graph.output]

    # load graph
    with open(os.path.join(os.path.dirname(onnx_file), FROZEN_GRAPH_FILENAME), 'rb') as f:
        graph_def = tf.compat.v1.GraphDef()
        graph_def.ParseFromString(f.read())

    # generate output data
    output_data = dict()
    tf_graph = tf.Graph()
    with tf_graph.as_default():
        tf.compat.v1.import_graph_def(graph_def=graph_def, name='')
        with tf.compat.v1.Session(graph=tf_graph) as sess:
            output_tensors = [tf_graph.get_tensor_by_name(k) for k in output_keys]
            feed_dict = {tf_graph.get_tensor_by_name(k): v for k, v in input_data.items()}
            tf_outputs = sess.run(output_tensors, feed_dict)
            for output_name, output_array in zip(output_keys, tf_outputs):
                output_data[output_name] = output_array

    return input_data, output_data


def validate_trt_engine(engine_file: str, onnx_file: str):
    logging.info('Start to validate TensorRT inference result...')
    input_data, output_data = make_test_data(onnx_file)

    trt_inference = TensorRTInference(engine_file)
    trt_inference.load()
    trt_outputs = trt_inference.infer(input_data)

    assert output_data.keys() == trt_outputs.keys(), \
        'TensorRT output keys mismatch, need %s, get %s' % \
        (str(output_data.keys()), str(trt_outputs.keys()))

    for k in output_data.keys():
        original_array = output_data[k]
        output_array = trt_outputs[k]
        assert original_array.shape == output_array.shape, \
            'Shape mismatch for input %s, need %s, get %s' % \
            (k, str(original_array.shape), str(output_array.shape))
        assert original_array.dtype == output_array.dtype, \
            'Dtype mismatch for input %s, need %s, get %s' % \
            (k, str(original_array.dtype), str(output_array.dtype))

        diff = np.abs(original_array - output_array)
        max_diff = float(np.max(diff))
        avg_diff = float(np.mean(diff))
        l1_distance = float(np.sum(diff))
        l2_distance = float(np.sqrt(np.sum(np.square(diff))))
        logging.info('Output name: %s, shape: %s, dtype: %s' %
                     (k, str(original_array.shape), str(original_array.dtype)))
        logging.info('max diff: %.10f, avg diff: %.10f, L1 distance: %.10f, L2 distance: %.10f' %
                     (max_diff, avg_diff, l1_distance, l2_distance))


if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    engine_file = sys.argv[1]
    onnx_file = sys.argv[2]
    validate_trt_engine(engine_file, onnx_file)
