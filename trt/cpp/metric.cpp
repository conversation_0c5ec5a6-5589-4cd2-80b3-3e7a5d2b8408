#include "metric.h"

#include <cmath>
#include <sys/time.h>

#include <numeric>

double percentile(const double *data, int count, double q) {
  double pos = (count - 1) * q;
  int left_pos = static_cast<int>(floor(pos));
  int right_pos = static_cast<int>(ceil(pos));
  double left_val = data[left_pos];
  double right_val = data[right_pos];

  double val = left_val * (right_pos - pos) + right_val * (pos - left_pos);
  return val;
}

double average(const double *data, int count) {
  double sum = std::accumulate(data, &(data[count]), 0.0);
  return sum / count;
}

double get_timestamp() {
  timeval t;
  gettimeofday(&t, nullptr);
  return (static_cast<double>(t.tv_sec) * 1000) + (static_cast<double>(t.tv_usec) / 1000);
}
