#pragma once

#include <string>
#include <fstream>
#include <vector>
#include <map>
#include <numeric>

enum Dtype {
  DTYPE_BOOL,
  DTYPE_FLOAT16,
  DTYPE_FLOAT32,
  DTYPE_FLOAT64,
  DTYPE_INT8,
  DTYPE_INT32,
  DTYPE_INT64,
};

class Tensor {
 public:
  Tensor(Dtype dtype, const std::vector<int64_t> &shape);
  int get_element_size();

  ~Tensor() {
    free(data);
  }

  Dtype get_dtype() {
    return dtype;
  }

  std::vector<int64_t> get_shape() {
    return shape;
  }

  int get_element_count() {
    return std::accumulate(shape.begin(), shape.end(), 1, std::multiplies<int>());
  }

  void *get_data_ptr() {
    return data;
  }

  int get_buffer_size() {
    return buffer_size;
  }

 private:
  Dtype dtype;
  std::vector<int64_t> shape;
  void *data;
  int buffer_size;
};

Tensor *parse_tensor(const std::string &name, std::ifstream &fs);
int parse_inputs_outputs(const std::string &filename,
    std::map<std::string, Tensor *> &inputs,
    std::map<std::string, Tensor *> &outputs);
int parse_weights(const std::string &filename, std::map<std::string, Tensor *> &weights);
