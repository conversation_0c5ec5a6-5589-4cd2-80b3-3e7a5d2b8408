#include "parse_data.h"

#include <iostream>
#include <map>
#include <sstream>
#include <string>
#include <unordered_set>
#include <vector>

#include "log.h"

int Tensor::get_element_size() {
  switch (dtype) {
    case DTYPE_BOOL:
      return 1;
    case DTYPE_FLOAT16:
      return 2;
      break;
    case DTYPE_FLOAT32:
    case DTYPE_INT32:
      return 4;
      break;
    case DTYPE_FLOAT64:
    case DTYPE_INT64:
      return 8;
      break;
  }
  LOG_ERROR("unknown dtype: ", dtype);
  return -1;
}

Tensor::Tensor(Dtype dtype, const std::vector<int64_t> &shape) {
  this->dtype = dtype;
  this->shape.resize(shape.size());
  std::copy(shape.begin(), shape.end(), this->shape.begin());

  int element_size = get_element_size();
  int element_count = get_element_count();
  int size = element_size * element_count;
  this->data = malloc(size);
  this->buffer_size = size;
}

Tensor *parse_tensor(const std::string &name, std::ifstream &fs) {
  std::string dummy;
  std::getline(fs, dummy);
  std::stringstream dim_ss(dummy);

  int dim_count;
  dim_ss >> dim_count;

  std::vector<int64_t> shape;

  if (dim_count == 0) {
    // scalar
    shape.push_back(0);
  } else {
    for (int i = 0; i < dim_count; ++i) {
      int dim;
      dim_ss >> dim;
      shape.push_back(dim);
    }
  }

  Tensor *tensor = new Tensor(DTYPE_FLOAT32, shape);
  int element_count = tensor->get_element_count();

  std::getline(fs, dummy);
  std::stringstream data_ss(dummy);
  for (int i = 0; i < element_count; ++i) {
    float *data_ptr = static_cast<float *>(tensor->get_data_ptr());
    data_ss >> data_ptr[i];
  }

  return tensor;
}

int parse_inputs_outputs(const std::string &filename,
    std::map<std::string, Tensor *> &inputs,
    std::map<std::string, Tensor *> &outputs) {
  // inputs
  std::ifstream ifs(filename);
  std::string dummy;
  std::getline(ifs, dummy);
  if (dummy != "*** inputs begin ***") {
    LOG_ERROR("Need \"*** inputs begin ***\", get: %s", dummy.c_str());
    return -1;
  }

  std::getline(ifs, dummy);
  int input_count = std::stoi(dummy);
  for (int i = 0; i < input_count; ++i) {
    std::string tensor_name;
    std::getline(ifs, tensor_name);
    Tensor *tensor = parse_tensor(tensor_name, ifs);
    inputs[tensor_name] = tensor;
  }

  // outputs
  std::getline(ifs, dummy);
  if (dummy != "*** preds begin ***") {
    LOG_ERROR("Need \"*** preds begin ***\", get: %s", dummy.c_str());
    return -1;
  }

  std::getline(ifs, dummy);
  int output_name_count = std::stoi(dummy);
  std::vector<std::string> output_names;
  for (int i = 0; i < output_name_count; ++i) {
    std::string output_name;
    std::getline(ifs, output_name);
    output_names.push_back(std::move(output_name));
  }

  std::getline(ifs, dummy);
  if (dummy != "*** outputs begin ***") {
    LOG_ERROR("Need \"*** outputs begin ***\", get: %s", dummy.c_str());
    return -1;
  }

  std::getline(ifs, dummy);
  int output_tensor_count = std::stoi(dummy);
  if (output_tensor_count != output_name_count) {
    LOG_ERROR("Get %d output names but %d output tensors", output_name_count, output_tensor_count);
    return -1;
  }

  for (int i = 0; i < output_tensor_count; ++i) {
    std::string output_name = output_names[i];
    Tensor *tensor = parse_tensor(output_name, ifs);
    outputs[output_name] = tensor;
  }

  return 0;
}

int parse_weights(const std::string &filename, std::map<std::string, Tensor *> &weights) {
  std::ifstream ifs(filename);
  std::string dummy;
  std::getline(ifs, dummy);
  if (dummy != "*** inputs begin ***") {
    LOG_ERROR("Need \"*** inputs begin ***\", get: %s", dummy.c_str());
    return -1;
  }

  std::getline(ifs, dummy);
  int input_count = std::stoi(dummy);
  for (int i = 0; i < input_count; ++i) {
    std::string tensor_name;
    std::getline(ifs, tensor_name);
    Tensor *tensor = parse_tensor(tensor_name, ifs);
    weights[tensor_name] = tensor;
  }
  return 0;
}
