#include <cstdlib>

#include <algorithm>
#include <fstream>
#include <map>
#include <thread>
#include <unordered_map>
#include <vector>

#include "cuda_runtime.h"
#include "NvInfer.h"

#include "log.h"
#include "metric.h"
#include "parse_data.h"
#include "trt_utils.h"

// one buffer for each thread
std::vector<nvinfer1::DataType> dtype;
std::vector<std::vector<int> > shape;
std::vector<int> buffer_size;
std::vector<std::vector<void *> > buffers;

std::unordered_map<std::string, int> input_binding_index;
std::unordered_map<std::string, int> output_binding_index;

nvinfer1::ICudaEngine *engine;
std::vector<nvinfer1::IExecutionContext *> contexts;
std::vector<cudaStream_t *> streams;

const int warmup_count = 100;
const int device_align = 16;

nvinfer1::ICudaEngine* load_trt_engine(nvinfer1::IRuntime* runtime, const std::string &filename) {
  std::ifstream engine_file(filename, std::ios::binary);
  if (!engine_file) {
    LOG_ERROR("cannot open engine file %s", filename.c_str());
    return nullptr;
  }

  engine_file.seekg(0, std::ios::end);
  int64_t size = engine_file.tellg();
  engine_file.seekg(0, std::ios::beg);

  std::vector<char> serialized_engine(size);
  engine_file.read(serialized_engine.data(), size);
  if (!engine_file) {
    return nullptr;
  }
  return runtime->deserializeCudaEngine(serialized_engine.data(), size, nullptr);
}

void allocate_buffers(int thread_count) {
  buffers.resize(thread_count);

  int binding_count = engine->getNbBindings();
  for (int i = 0; i < binding_count; ++i) {
    std::string binding_name = engine->getBindingName(i);
    if (engine->bindingIsInput(i)) {
      input_binding_index[binding_name] = i;
    } else {
      output_binding_index[binding_name] = i;
    }

    nvinfer1::DataType data_type = engine->getBindingDataType(i);
    dtype.push_back(data_type);
    std::vector<int> dims;
    for (int j = 0; j < engine->getBindingDimensions(i).nbDims; ++j) {
      dims.push_back(engine->getBindingDimensions(i).d[j]);
    }
    shape.push_back(dims);

    int size = get_datatype_size(data_type) * get_shape_size(dims);
    buffer_size.push_back(size);

    for (int j = 0; j < thread_count; ++j) {
      void *buffer = nullptr;
      CudaSafeCall(cudaMalloc(&buffer, size));
      buffers[j].push_back(buffer);
    }
  }
}

void load_model(const std::string &engine_file, int thread_count) {
  nvinfer1::IRuntime *runtime = nvinfer1::createInferRuntime(gLogger);
  engine = load_trt_engine(runtime, engine_file);
  runtime->destroy();
  if (engine == nullptr) {
    LOG_ERROR("error load engine file %s", engine_file.c_str());
    exit(1);
  }

  // allocate buffers
  allocate_buffers(thread_count);

  // create context and stream
  for (int i = 0; i < thread_count; ++i) {
    nvinfer1::IExecutionContext *context = engine->createExecutionContext();
    contexts.push_back(context);
    cudaStream_t *stream = new cudaStream_t();
    CudaSafeCall(cudaStreamCreate(stream));
    streams.push_back(stream);
  }
}

void inference(std::vector<std::string> &input_names, std::vector<Tensor *> &input_tensors,
    std::vector<std::string> &output_names, std::vector<Tensor *> &output_tensors, int thread_id) {
  cudaStream_t* stream = streams[thread_id];

  // copy memory to gpu
  int input_count = input_names.size();
  for (int i = 0; i < input_count; ++i) {
    CudaSafeCall(cudaMemcpyAsync(buffers[thread_id][input_binding_index[input_names[i]]],
                                 input_tensors[i]->get_data_ptr(),
                                 input_tensors[i]->get_buffer_size(),
                                 cudaMemcpyHostToDevice,
                                 *stream));
  }

  // execute
  contexts[thread_id]->enqueueV2(buffers[thread_id].data(), *stream, nullptr);

  // copy memory from gpu
  int output_count = output_names.size();
  for (int i = 0; i < output_count; ++i) {
    CudaSafeCall(cudaMemcpyAsync(output_tensors[i]->get_data_ptr(),
                                 buffers[thread_id][output_binding_index[output_names[i]]],
                                 output_tensors[i]->get_buffer_size(),
                                 cudaMemcpyDeviceToHost,
                                 *stream));
  }
}

void run_inference(std::vector<std::string> &input_names,
    std::vector<Tensor *> &input_tensors,
    std::vector<std::string> &output_names,
    std::vector<Tensor *> &output_buffers,
    int thread_id,
    int run_count,
    double *time_used) {
  std::vector<double> warmup_time;
  for (int i = 0; i < run_count; ++i) {
    double timestamp_s = get_timestamp();
    inference(input_names, input_tensors, output_names, output_buffers, thread_id);
    double timestamp_e = get_timestamp();

    if (time_used) {
      time_used[i] = timestamp_e - timestamp_s;
    }
  }
}

int main(int argc, char *argv[]) {
  // args: engine_path, data_path, test_thread, run_count
  if (argc < 5) {
    LOG_ERROR("arguments: engine_path, data_path, test_thread, run_count");
    return 1;
  }

  const char *engine_path = argv[1];
  const char *data_path = argv[2];
  int test_thread = atoi(argv[3]);
  int run_count = atoi(argv[4]);

  // load model
  load_model(engine_path, test_thread);

  // data
  std::map<std::string, Tensor *> inputs, outputs;
  parse_inputs_outputs(data_path, inputs, outputs);
  std::vector<std::string> input_names;
  std::vector<Tensor *> input_tensors;
  for (const auto &iter : inputs) {
    // add suffix
    std::string name_with_suffix = iter.first + ":0";
    if (input_binding_index.find(name_with_suffix) != input_binding_index.end()) {
      input_names.push_back(name_with_suffix);
      input_tensors.push_back(iter.second);
    }
  }

  std::vector<std::string> output_names;
  std::vector<Tensor *> output_tensors, output_buffers;
  for (const auto &iter : outputs) {
    // add suffix
    std::string name_with_suffix = iter.first + ":0";
    if (output_binding_index.find(name_with_suffix) != output_binding_index.end()) {
      output_names.push_back(name_with_suffix);
      output_tensors.push_back(iter.second);
      output_buffers.push_back(new Tensor(DTYPE_FLOAT32, iter.second->get_shape()));
    }
  }

  // warmup
  run_inference(input_names, input_tensors, output_names, output_buffers, 0, 100, nullptr);

  // thread pool
  std::vector<std::thread *> threads;
  int total_count = test_thread * run_count;
  double *time_used = new double[total_count];
  for (int i = 0; i < test_thread; ++i) {
    threads.push_back(new std::thread([&, i, run_count](){
      run_inference(input_names, input_tensors, output_names, output_buffers,
        i, run_count, &(time_used[i * run_count]));
    }));
  }

  for (int i = 0; i < test_thread; ++i) {
    threads[i]->join();
  }

  // calculate metrics
  std::sort(time_used, &(time_used[total_count]));
  double p50 = percentile(time_used, total_count, 0.5);
  double p95 = percentile(time_used, total_count, 0.95);
  double p99 = percentile(time_used, total_count, 0.99);
  double avg = average(time_used, total_count);
  LOG_INFO("average: %.2fms, p50: %.2fms, p95: %.2fms, p99: %.2fms", avg, p50, p95, p99);
  double qps = test_thread * 1000 / avg;
  LOG_INFO("QPS: %.2f", qps);
  return 0;
}
