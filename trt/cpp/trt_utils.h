#pragma once

#include <cstdlib>
#include <iostream>
#include <numeric>
#include <vector>

#include "cuda_runtime.h"
#include "NvInfer.h"

#include "log.h"

#define CudaSafeCall(err) __cudaSafeCall(err, __FILE__, __LINE__)

inline void __cudaSafeCall(cudaError err, const char *file, const int line) {
  if (err != cudaSuccess) {
    LOG_ERROR("cudaSafeCall() failed at %s:%d, error code: %d, error msg: %s",
      file, line, err, cudaGetErrorString(err));
    exit(-1);
  }
}

class TRTLogger : public nvinfer1::ILogger {
  void log(Severity severity, const char* msg) noexcept override {
    // suppress warning-level messages
    if (severity == Severity::kERROR || severity == Severity::kINTERNAL_ERROR) {
      LOG_ERROR("TENSORRT ERROR: %s", msg);
    }
  }
};

static TRTLogger gLogger;

inline int get_datatype_size(nvinfer1::DataType type) {
  switch (type) {
    case nvinfer1::DataType::kFLOAT:
      return sizeof(float);
    case nvinfer1::DataType::kINT32:
      return sizeof(int32_t);
    case nvinfer1::DataType::kHALF:
      return sizeof(int16_t);
    case nvinfer1::DataType::kINT8:
      return sizeof(int8_t);
    case nvinfer1::DataType::kBOOL:
      return sizeof(bool);
  }
}

inline int get_shape_size(const std::vector<int> &dims) {
  return std::accumulate(dims.begin(), dims.end(), 1, std::multiplies<int>());
}
