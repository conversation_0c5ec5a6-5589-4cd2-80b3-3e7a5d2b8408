#pragma once

#include <cstdio>

#define __output(...) \
  printf(__VA_ARGS__)

#define __info_format(__fmt__) "[INFO] %s(%d)-<%s>: " __fmt__ "\n"
#define __warn_format(__fmt__) "[WARN] %s(%d)-<%s>: " __fmt__ "\n"
#define __error_format(__fmt__) "[ERROR] %s(%d)-<%s>: " __fmt__ "\n"

#define LOG_INFO(__fmt__, ...) \
  __output(__info_format(__fmt__), __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

#define LOG_WARN(__fmt__, ...) \
  __output(__warn_format(__fmt__), __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

#define LOG_ERROR(__fmt__, ...) \
  __output(__error_format(__fmt__), __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
