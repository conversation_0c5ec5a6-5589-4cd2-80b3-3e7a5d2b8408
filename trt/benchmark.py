import os


class BenchmarkResult(object):
    def __init__(self, p50: float, p95: float, p99: float, avg: float, qps: float):
        self.p50 = p50
        self.p95 = p95
        self.p99 = p99
        self.avg = avg
        self.qps = qps


def parse_result(lines):
    qps = float(lines[-1].strip().split(':')[-1])
    time_fields = lines[-2].strip().split(',')

    parsed_time = list()
    for time_str in time_fields:
        time = float(time_str.rstrip('ms').split(':')[-1])
        parsed_time.append(time)

    avg, p50, p95, p99 = parsed_time
    result = BenchmarkResult(p50, p95, p99, avg, qps)
    return result


def run_benchmark(engine_file: str, data_file: str, num_threads: int = 1, run_count: int = 1000):
    cmd = './trt_benchmark %s %s %d %d' % (engine_file, data_file, num_threads, run_count)
    with os.popen(cmd, 'r') as f:
        lines = f.readlines()

    experiment = parse_result(lines)
    return experiment
