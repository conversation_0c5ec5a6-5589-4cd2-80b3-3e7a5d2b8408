import logging
import os
import sys

import numpy as np
import onnx
import tensorrt as trt

def refit_trt_engine(original_engine_file: str, original_onnx_file: str, onnx_file: str, output_engine_file: str):
    logging.info('Refitting TensorRT engine file, original engine: %s, original onnx file: %s, new onnx file: %s, output engine: %s' %
                 (original_engine_file, original_onnx_file, onnx_file, output_engine_file))

    trt_logger = trt.Logger(trt.Logger.Severity.ERROR)
    trt.init_libnvinfer_plugins(trt_logger, '')
    with open(original_engine_file, 'rb') as f_engine, trt.Runtime(trt_logger) as runtime:
        engine = runtime.deserialize_cuda_engine(f_engine.read())

    # 已转版本的 onnx
    original_onnx_model = onnx.load_model(original_onnx_file)
    original_onnx_weights = {i.name: i for i in original_onnx_model.graph.initializer}
    logging.info('original onnx weights num: %d' % len(original_onnx_weights))

    # 待转版本的 onnx
    new_onnx_model = onnx.load_model(onnx_file)
    new_onnx_weights = {i.name: i for i in new_onnx_model.graph.initializer}
    logging.info('new onnx weights num: %d' % len(new_onnx_weights))

    refitter = trt.Refitter(engine, trt_logger)
    for weight_name in refitter.get_all_weights():
        # convert onnx weight proto to ndarray
        if weight_name in new_onnx_weights.keys():
            if weight_name not in original_onnx_weights.keys():
                logging.info('Weight not exist in original onnx model, tensor name: %s' % weight_name)
                continue
            # 检查数据类型是否一致
            original_data_type = original_onnx_weights[weight_name].data_type
            original_dims = original_onnx_weights[weight_name].dims
            data_type = new_onnx_weights[weight_name].data_type
            dims = new_onnx_weights[weight_name].dims

            if (original_data_type != data_type) or (original_dims != dims):
                logging.info('Different data type or dims, tensor name: %s, original data type: %s, original dims: %s, new data type: %s, new dims: %s'
                             % (weight_name, original_data_type, original_dims, data_type, dims))
                continue

            # 根据数据类型解析数据
            if data_type == onnx.TensorProto.FLOAT:
                data = np.frombuffer(new_onnx_weights[weight_name].raw_data, dtype=np.float32).reshape(dims)
            elif data_type == onnx.TensorProto.INT32:
                data = np.frombuffer(new_onnx_weights[weight_name].raw_data, dtype=np.int32).reshape(dims)
            elif data_type == onnx.TensorProto.INT64:
                # tensorrt 不支持 int64 类型的 refit，这里强制转换为 int32
                logging.info('Cast dtype from int64 to int32, tensor name: %s' % weight_name)
                data = np.frombuffer(new_onnx_weights[weight_name].raw_data, dtype=np.int64).reshape(dims).astype(np.int32)
            else:
                logging.info('Unsupported data type: %s' % data_type)
                continue
            refitter.set_named_weights(weight_name, data)

    missing_weights = refitter.get_missing_weights()
    assert len(missing_weights) == 0, 'Missing weights: %s' % str(missing_weights)

    assert refitter.refit_cuda_engine(), 'Call refit_cuda_engine fail'

    with open(output_engine_file, 'wb') as f_engine:
        f_engine.write(engine.serialize())

if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    original_engine_file = sys.argv[1]
    original_onnx_file = sys.argv[2]
    new_onnx_file = sys.argv[3]
    output_engine_file = sys.argv[4]
    refit_trt_engine(original_engine_file, original_onnx_file, new_onnx_file, output_engine_file)
