#!/usr/bin/env python
# -*- coding: UTF-8 -*-

"""
开发者插件demo,基于此demo修改execute方法即可开发自家的插件

该模块需要定义如下class：
- `TaskExecutorProvider`，继承sdk中的ExecutorProvider
- `TaskExecutor`，继承sdk中的Executor
- `TaskJobParameter`，继承sdk中的JobParameter
- `BoreTFPlugin`，继承sdk中的BaseOperator
"""
import json
import logging
import os
import subprocess
import sys
import time

from sdk import JobParameter, ExecutorProvider, Executor, BaseOperator
from components import const, model_utils
from components.const import ConvertType, OpenApiHost
from components.convert_manager import update_convert_manager_err_msg
from main import Routine
from utils.download_utils import download_with_retry
from utils import trt_utils, cuda_utils


class TaskExecutorProvider(ExecutorProvider):
    """
    容器环境下调起其他容器工作的接口
    Methods:
        add_executor(self, executor: Executor) -> None
        get_task_excecutor(self, executor_name: str) -> Executor
    """

    def __init__(self):
        """
        构造器
        完全继承父类方法
        """
        super().__init__()


class TaskExecutor(Executor):
    """
    获取自定义组件参数，调起后台资源（e.g. Bore, TensorFlow jobs），并且将
    组件参数传给组件运行时使用
    Methods:
    get_name(self) -> str
    submit_task(self, executor_parameter: ExecutorParameter, executor_provider) -> object:
    """

    def __init__(self):
        """
        构造器
        完全继承父类方法
        """
        super().__init__()


class TaskJobParameter(JobParameter):
    """
    是自定义组件运行时需要的所有参数集合，是平台用户对作业的配置，
    开放给开发者
    Methods:
        __init__(self, parameters: dict)
        __setitem__(self, key: str, value: str) -> None
        _getitem__(self, key: str) -> str
        __contains__(self, key: str) -> bool
    """

    def __init__(self, parameter: dict) -> None:
        """
        构造器
        完全继承父类方法
        """
        super().__init__(parameter)


class BorePyTorchPlugin(BaseOperator):
    """
    开发bore pytorch插件时，开发者需基于BaseOperator实现BoreTFPlugin类
    Methods
        __init__(self, executor_provider: ExecutorProvider)
        pre_config(self, job_parameter: JobParameter) -> None
        post_config(self, job_parameter: JobParameter) -> None
        pre_execute(self, job_parameter: JobParameter) -> None
        execute(self, job_parameter: JobParameter) -> object
        post_execute(self, job_parameter: JobParameter) -> None
        validate(self, job_parameter: JobParameter) -> bool
        get_status(self, job_parameter: JobParameter) -> object
        kill(self, job_parameter: JobParameter) -> bool
    """

    def execute(self, job_parameter: JobParameter) -> None:
        """
        插件启动方法，开发者主要修改此方法
        """
        logging.basicConfig(
            format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
            level=logging.INFO, stream=sys.stdout, force=True
        )

        # 高级选项
        params_pairs = job_parameter.dict['paramsPairs']
        try:
            params_pairs_dict = json.loads(params_pairs)
            for key, value in params_pairs_dict.items():
                job_parameter.dict[key] = value
        except Exception as e:  # pylint: disable=broad-except
            logging.error(f'paramsPairs is invalid:{e}')
        is_debug = False
        if 'is_debug' in job_parameter.dict.keys():
            is_debug = bool(job_parameter.dict["is_debug"])
        if 'by_plugin' in job_parameter.dict.keys():
            const.TRT_GENERATE_ENGINE_BY_EVART_TOOL = not bool(job_parameter.dict["by_plugin"])
        if 'use_evart_tool' in job_parameter.dict.keys():
            const.TRT_GENERATE_ENGINE_BY_EVART_TOOL = bool(job_parameter.dict["use_evart_tool"])
        # TODO(benshen): 后续支持重复转模
        if 'allow_repeat_convert' in job_parameter.dict.keys():
            const.ALLOW_REPEAT_CONVERT = bool(job_parameter.dict["allow_repeat_convert"])
        const.OPEN_API_HOST = OpenApiHost.FORMAL.value
        if 'env' in job_parameter.dict.keys():
            env = job_parameter.dict["env"]
            if env == 'pre':
                const.OPEN_API_HOST = OpenApiHost.PRE.value
            if env == 'test':
                const.OPEN_API_HOST = OpenApiHost.TEST.value
        opset_version = 9
        if 'opset_version' in job_parameter.dict.keys():
            opset_version = int(job_parameter.dict["opset_version"])
        user_inputs = item_inputs = ''
        if 'user_inputs' in job_parameter.dict.keys():
            user_inputs = job_parameter.dict["user_inputs"]
        if 'item_inputs' in job_parameter.dict.keys():
            item_inputs = job_parameter.dict["item_inputs"]

        convert_with_refit = False
        if 'convert_with_refit' in job_parameter.dict.keys():
            convert_with_refit = bool(job_parameter.dict["convert_with_refit"])
        # 全量模型也强制通过refit转模
        force_convert_with_refit = False
        if 'force_convert_with_refit' in job_parameter.dict.keys():
            force_convert_with_refit = bool(job_parameter.dict["force_convert_with_refit"])
            if force_convert_with_refit:
                convert_with_refit = True

        if is_debug:
            logging.info("======== print all parameter ========")
            for key in job_parameter.dict:
                logging.info(f" === {key} : {job_parameter[key]}")

        # parse and check parameters
        group_id = int(job_parameter.dict['f_app_group_id'])
        job_id = int(job_parameter.dict['f_job_id'])
        session_id = job_parameter.dict['f_session_id']
        appinstance_name = job_parameter.dict['appinstance_name']
        original_model_name = job_parameter.dict['modelName']
        model_version = job_parameter.dict['modelVersion']
        output_model_name = job_parameter.dict['outputModelName']
        predict_target = job_parameter.dict['predictTarget']
        keep_version = int(job_parameter.dict['keepVersion'])
        batch_size = job_parameter.dict['batchSize']
        placeholder_replace = job_parameter.dict['placeholderReplace']
        engine_type = job_parameter.dict["engine_type"]
        fp16 = bool(job_parameter.dict['fp16'])
        use_refit = bool(job_parameter.dict["use_refit"])
        resource_group_name = job_parameter.dict["boreConfig.resourceGroupName"]

        cos_path = "numerous_v2/default/" + output_model_name

        # register switch
        is_register = True
        if "is_register" in job_parameter.dict.keys():
            is_register = job_parameter.dict["is_register"]

        # read config
        const.VENUS_OPENAPI_SECRET_ID = job_parameter.secret['venus_openapi_sid']
        const.VENUS_OPENAPI_SECRET_KEY = job_parameter.secret['venus_openapi_skey']
        if const.OPEN_API_HOST == OpenApiHost.PRE.value:
            const.USER_OPENAPI_SECRET_ID = job_parameter.secret['pre_venus_openapi_sid']
            const.USER_OPENAPI_SECRET_KEY = job_parameter.secret['pre_venus_openapi_skey']
        if const.OPEN_API_HOST == OpenApiHost.TEST.value:
            const.USER_OPENAPI_SECRET_ID = job_parameter.secret['test_venus_openapi_sid']
            const.USER_OPENAPI_SECRET_KEY = job_parameter.secret['test_venus_openapi_sid']
        const.VENUS_OP_CODE = job_parameter.dict["op_code"]
        const.VENUS_OP_VERSION = job_parameter.dict["op_version"]
        const.OPERATOR = job_parameter.dict["job_owner"]

        # 系统环境变量设置
        const.TRT_VERSION = trt_utils.get_trt_version()
        const.CUDA_VERSION = cuda_utils.get_cuda_version()
        const.GPU_DEVICE_TYPE = cuda_utils.get_device_type()
        if 'task_id' in job_parameter.dict.keys():
            const.TASK_ID = job_parameter.dict["task_id"]
        run_test_bs = 0
        if 'run_test_bs' in job_parameter.dict.keys():
            run_test_bs = int(job_parameter.dict["run_test_bs"])
        sleep_before_convert = 0
        if 'sleep_before_convert' in job_parameter.dict.keys():
            sleep_before_convert = int(job_parameter.dict["sleep_before_convert"])
        sleep_after_convert = 0
        if 'sleep_after_convert' in job_parameter.dict.keys():
            sleep_after_convert = int(job_parameter.dict["sleep_after_convert"])

        logging.info(f'========== Parameter details ==========')
        logging.info(f'f_app_group_id: {group_id}')
        logging.info(f'f_job_id: {job_id}')
        logging.info(f'f_session_id: {session_id}')
        logging.info(f'modelName: {original_model_name}')
        logging.info(f'modelVersion: {model_version}')
        logging.info(f'outputModelName: {output_model_name}')
        logging.info(f'predictTarget: {predict_target}')
        logging.info(f'placeholderReplace: {placeholder_replace}')
        logging.info(f'keepVersion: {keep_version}')
        logging.info(f'cosPath: {cos_path}')
        logging.info(f'batchSize: {batch_size}')
        logging.info(f'engineType: {engine_type}')
        logging.info(f'fp16: {fp16}')
        logging.info(f'refit: {use_refit}')
        logging.info(f'resource_group_name: {resource_group_name}')
        logging.info(f'opset_version: {opset_version}')
        logging.info(f'user_inputs: {user_inputs}')
        logging.info(f'item_inputs: {item_inputs}')
        logging.info(f'convert_with_refit: {convert_with_refit}')
        logging.info(f'force_convert_with_refit: {force_convert_with_refit}')
        logging.info(f'use_evart_tool: {const.TRT_GENERATE_ENGINE_BY_EVART_TOOL}')
        logging.info(f'venus_op_code: {const.VENUS_OP_CODE}')
        logging.info(f'venus_op_version: {const.VENUS_OP_VERSION}')
        logging.info(f'open_api_host: {const.OPEN_API_HOST}')
        logging.info(f'task_id: {const.TASK_ID}')
        logging.info(f'run_test_bs: {run_test_bs}')
        logging.info(f'========== End of parameters ==========')
        logging.info('========== Start running NumerousTensorRT ==========')

        if is_register:
            logging.info('Setup Env...')
            subprocess.run('sudo chmod 775 start_env.sh', shell=True)
            subprocess.run("sudo sh -c './start_env.sh'", shell=True)

        if engine_type in ['trt', 'evart'] and const.VENUS_OP_CODE != 'ConvertWorker':
            # download tensorRT plugin library
            plugin_lib_path = '/TensorRT/lib/libnvinfer_plugin.so.8.6.1'
            sha1sum_code = model_utils.calc_sha1sum(plugin_lib_path)
            if sha1sum_code != const.TRT_LIBNVINFER_PLUGIN_SHA1SUM:
                logging.info('Downloading TensorRT plugin...')
                download_command = ('sudo wget -q https://mirrors.tencent.com/repository/generic/numerous_serving/'
                                    f'numerous_tensorrt/libnvinfer_plugin.so.8.6.1 -O "{plugin_lib_path}"')

                # 使用下载工具函数进行下载，包含重试逻辑
                download_success = download_with_retry(
                    download_command=download_command,
                    file_path=plugin_lib_path,
                    expected_sha1=const.TRT_LIBNVINFER_PLUGIN_SHA1SUM,
                    sha1_calculator=model_utils.calc_sha1sum,
                )

                if not download_success:
                    raise Exception('Failed to download TensorRT plugin after maximum retries.')

        const.CONVERT_TYPE = ConvertType.NUMEROUS_TRT.value
        if engine_type == "evart":
            const.CONVERT_TYPE = ConvertType.NUMEROUS_EVART.value

        # 通过evart工具转换前需要先下载工具: evart只能使用evart工具转模，trt可选择是否使用evart工具转模
        if (engine_type == "evart" or const.TRT_GENERATE_ENGINE_BY_EVART_TOOL) and const.VENUS_OP_CODE != 'ConvertWorker':
            # 组件v7.0.1 + evart v2.0.1
            evart_version = "v701_201_test1"
            const.ENGINE_VERSION = evart_version

            logging.info('Downloading EvaRT_%s tools...' % evart_version)
            evart_tools = "evart_script_" + evart_version + ".tar.gz"
            evart_tools_path = os.path.join(os.getcwd(), evart_tools)
            download_command = f"sudo wget -q https://mirrors.tencent.com/repository/generic/evart/{evart_tools} -O {evart_tools_path}"

            # 使用下载工具函数进行下载，包含重试逻辑
            download_success = download_with_retry(
                download_command=download_command,
                file_path=evart_tools_path,
            )

            if not download_success:
                raise Exception(f'Failed to download EvaRT tools {evart_tools} after maximum retries.')

            # 解压文件并清理
            try:
                subprocess.run(f"tar pzxvf {evart_tools} -C evart/", shell=True, check=True)
                subprocess.run(f"rm -rf {evart_tools}", shell=True, check=True)
                subprocess.run("sudo chmod 755 evart/evartEngineTest", shell=True, check=True)
                subprocess.run("sudo chmod 755 evart/evartConverterTool", shell=True, check=True)
            except subprocess.CalledProcessError as e:
                logging.error(f"Error processing EvaRT tools: {str(e)}")
                raise Exception(f"Failed to process EvaRT tools: {str(e)}")

        if sleep_before_convert > 0:
            logging.info(f'Sleep before convert: {sleep_before_convert} seconds')
            time.sleep(sleep_before_convert)

        exception_occurred = False
        try:
            # start running component
            routine = Routine(
                group_id=group_id,
                job_id=job_id,
                session_id=session_id,
                appinstance_name=appinstance_name,
                original_model_name=original_model_name,
                model_version=model_version,
                output_model_name=output_model_name,
                predict_target=predict_target,
                keep_version=keep_version,
                cos_path=cos_path,
                batch_size=batch_size,
                placeholder_replace=placeholder_replace,
                engine_type=engine_type,
                fp16=fp16,
                use_refit=use_refit,
                is_register=is_register,
                resource_group_name=resource_group_name,
                params_pairs=params_pairs,
                opset_version=opset_version,
                user_inputs=user_inputs,
                item_inputs=item_inputs,
                convert_with_refit=convert_with_refit,
                force_convert_with_refit=force_convert_with_refit,
                run_test_bs=run_test_bs)
            routine.main()
        except Exception as e:
            print(e)
            update_convert_manager_err_msg(output_model_name, model_version, str(e))
            exception_occurred = True

        if sleep_after_convert > 0:
            logging.info(f'Sleep after convert: {sleep_after_convert} seconds')
            time.sleep(sleep_after_convert)

        if exception_occurred:
            raise Exception('Mission Terminated...')
        else:
            logging.info('Mission Completed...')

    def validate(self, job_parameter: JobParameter) -> None:
        """
        校验参数
        """
        raise NotImplementedError

    def get_status(self, job_parameter: JobParameter) -> None:
        """
        获取任务执行状态
        """
        raise NotImplementedError

    def kill(self, job_parameter: JobParameter) -> None:
        """
        终止任务
        """
        raise NotImplementedError
