import argparse

from test_plugin import test_one_model

# 流水线测试所有模型
# 测试用例操作文档：https://doc.weixin.qq.com/doc/w3_AFUAgwaLAFcwTxbOM4XTJyaWTA09v?scode=AJEAIQdfAAo0lZj0cwAFUAgwaLAFc
# 建议把用户模型拷贝到平台模型中：https://venus.woa.com/#/subsystem/model/detail/NumerousTensorRT/instance
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="test all models")
    parser.add_argument("--proxy_user_secret_id", required=True)
    parser.add_argument("--proxy_user_secret_key", required=True)
    args = parser.parse_args()
    all_models = [{
        # QQ增长(2714)测试作业(816757724),拷贝自模型 AT_n_md_qqchannel_red_base_v3
        'modelVersion': '2024_10_17_001138',
    }, {
        # 腾讯视频推荐算法中心(477)测试作业(816678456),拷贝自模型 rcmd_long_homepage_weak_validplay_target_0108_esm2_v1
        'modelVersion': '2025_01_08_153454',
        'predictTarget': 'merge_predict',
        'paramsPairs': '{"use_evart_tool": true}',
    }, {
        # 腾讯视频推荐算法中心短视频推荐(418)测试作业(816741485),拷贝自模型 short_tab_base_0515_pepnet_v4
        'modelVersion': '2025_01_08_154811',
        'fp16': True,
    }, {
        # video(44)测试作业(816542391),拷贝自模型 feed_rank_model_gpu_v5_new_sample_hotkey
        'modelVersion': '2025_01_08_153752',
        'predictTarget': 'news_sigmoid;short_video_sigmoid;mini_video_sigmoid',
    }]
    for model in all_models:
        model['proxy_user_secret_id'] = args.proxy_user_secret_id
        model['proxy_user_secret_key'] = args.proxy_user_secret_key
        test_one_model(model)
