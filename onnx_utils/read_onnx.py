import json
import os
from typing import List

import onnx
import tensorrt as trt

from components import const


def get_unsupported_nodes_cpu_targets(onnx_path: str):
    trt_logger = trt.Logger(trt.Logger.Severity.INFO)
    network_creation_flag = (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH)) | \
                            (1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_PRECISION))
    parser = trt.OnnxParser(trt.Builder(trt_logger).create_network(network_creation_flag), trt_logger)
    # 加载 ONNX 模型
    model = onnx.load(onnx_path)
    # 遍历所有图节点并找出不支持算子
    unsupported_nodes = []
    unsupported_nodes_outputs = []
    for node in model.graph.node:
        node_type = node.op_type
        node_name = node.name
        outputs = node.output
        if parser.supports_operator(node_type):
            continue
        if node_type in const.ONNX_OPERATOR_WHITE_LIST:
            continue
        unsupported_nodes.append(node_name)
        unsupported_nodes_outputs.extend(outputs)
    # 找出不支持算子的下一个节点作为cpu_target
    cpu_targets = []
    for node in model.graph.node:
        node_name = node.name
        if node_name in unsupported_nodes:
            continue
        inputs = node.input
        for input in inputs:
            if input in unsupported_nodes_outputs:
                cpu_targets.append(node_name)
                break
    return cpu_targets


def get_dynamic_nodes_cpu_targets():
    if os.path.exists(const.EVA_RESULT_FILE_NAME):
        with open(const.EVA_RESULT_FILE_NAME, 'r') as file:
            data = json.load(file)
            if 'dynamicNodesCPUTargets' not in data.keys():
                return []
            cpu_targets_str = data['dynamicNodesCPUTargets']
            if cpu_targets_str == '':
                return []
            return cpu_targets_str.split(',')
    else:
        return []


def get_inputs_shape(onnx_path: str, inputs: List[str]):
    # 加载 ONNX 模型
    graph = onnx.load(onnx_path).graph
    inputs_shape = dict()
    for input_info in graph.input:
        if input_info.name in inputs:
            dim_values = []
            dims = input_info.type.tensor_type.shape.dim
            for dim in dims:
                dim_values.append(dim.dim_value)
            inputs_shape[input_info.name]=dim_values
    return inputs_shape