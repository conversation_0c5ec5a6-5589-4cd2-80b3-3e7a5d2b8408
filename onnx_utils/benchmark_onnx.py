import os
import subprocess

from components import const
from components.const import PYTHON_RUN


def _get_env():
    env = os.environ.copy()
    env['PYTHONPATH'] = '%s:%s' % \
                        (os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), env.get('PYTHONPATH', ''))
    return env


def export_onnx(input_path: str, output_path: str, batch_size: int, input_target: str, predict_target: str,
                engine_type: str, opset_version: int):
    script_path = os.path.join(os.path.dirname(__file__), 'build_onnx.py')
    command = '"%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"' % \
              (PYTHON_RUN, script_path, input_path, output_path, batch_size, input_target, predict_target, engine_type,
               opset_version, const.TRT_GENERATE_ENGINE_BY_EVART_TOOL)
    p = subprocess.run(command, shell=True, stdout=subprocess.PIPE, env=_get_env())

    # print tf2onnx subprocess log to stdout
    tf2onnx_log = p.stdout.decode('utf-8')
    print(tf2onnx_log)

    # check subprocess error
    if p.returncode != 0:
        raise Exception('Command %s fail with code %d' % (command, p.returncode))


def infer_onnx_shape(onnx_file: str):
    # 该进程失败对后续无影响
    script_path = os.path.join(os.path.dirname(__file__), 'symbolic_shape_infer.py')
    subprocess.run('"%s" "%s" '
                   '--input %s --output %s' % (PYTHON_RUN, script_path, onnx_file, onnx_file),
                   shell=True, env=_get_env())
