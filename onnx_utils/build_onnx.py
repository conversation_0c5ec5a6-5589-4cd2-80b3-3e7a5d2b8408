import logging
import os
import sys
from collections import OrderedDict

import onnx

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '0'
import tensorflow as tf

from tensorflow.core.framework import graph_pb2
from tf2onnx import optimizer
from tf2onnx.convert import process_tf_graph

from tf2onnx.optimizer.const_fold_optimizer import ConstFoldOptimizer
from tf2onnx.optimizer.einsum_optimizer import EinsumOptimizer
from tf2onnx.optimizer.identity_optimizer import IdentityOptimizer
from tf2onnx.optimizer.merge_duplicated_nodes_optimizer import MergeDuplicatedNodesOptimizer
from tf2onnx.optimizer.transpose_optimizer import TransposeOptimizer
from tf2onnx.optimizer.loop_optimizer import LoopOptimizer
from tf2onnx.optimizer.back_to_back_optimizer import BackToBackOptimizer
from tf2onnx.optimizer.upsample_optimizer import UpsampleOptimizer
from tf2onnx.optimizer.const_dequantize_optimizer import ConstDequantizeOptimizer
from tf2onnx.optimizer.reshape_optimizer import ReshapeOptimizer
from tf2onnx.optimizer.global_pool_optimizer import GlobalPoolOptimizer
from tf2onnx.optimizer.q_dq_optimizer import QDQOptimizer

optimizers = OrderedDict([
    ("optimize_transpose", TransposeOptimizer),
    ("remove_redundant_upsample", UpsampleOptimizer),
    ("fold_constants", ConstFoldOptimizer),
    ("const_dequantize_optimizer", ConstDequantizeOptimizer),
    ("loop_optimizer", LoopOptimizer),
    # merge_duplication should be used after optimize_transpose
    # for optimize_transpose may have some trans nodes that can be merge
    ("merge_duplication", MergeDuplicatedNodesOptimizer),
    # disable reshape optimizer
    ("reshape_optimizer", ReshapeOptimizer),
    ("global_pool_optimizer", GlobalPoolOptimizer),
    ("q_dq_optimizer", QDQOptimizer),
    ("remove_identity", IdentityOptimizer),
    ("remove_back_to_back", BackToBackOptimizer),
    ("einsum_optimizer", EinsumOptimizer),
])


def save_graph(graph_def: graph_pb2.GraphDef, output_filename: str):
    with open(output_filename, 'wb') as f:
        f.write(graph_def.SerializeToString())


def export_to_onnx(input_path: str, output_path: str, batch_size: int, input_target: str, predict_target: str,
                   opset_version: int, use_evart_tool: bool):
    enable_optimize = True

    # load file
    with open(input_path, 'rb') as f:
        pb_graph = graph_pb2.GraphDef()
        pb_graph.ParseFromString(f.read())

    frozen_graph = graph_pb2.GraphDef()
    frozen_graph.CopyFrom(pb_graph)

    logging.info('Converting TF graph to ONNX...')
    with tf.Graph().as_default() as tf_graph:
        tf.import_graph_def(frozen_graph, name='')
        g = process_tf_graph(tf_graph,
                             input_names=[k + ':0' for k in input_target],
                             output_names=[k + ':0' for k in predict_target], opset=opset_version)

    if enable_optimize:
        # g = optimizer.optimize_graph(g)
        g = optimizer.optimize_graph(g, True, optimizers)
    onnx_model = g.make_model('')

    if use_evart_tool or engine_type == 'evart':
        file_path, _ = os.path.split(output_path)
        onnx_original_file = os.path.join(file_path, 'model_nobatch.onnx')
        onnx.save_model(onnx_model, onnx_original_file)
        logging.info('Save original %s ...' % onnx_original_file)

        logging.info('Set onnx model input dim[0] as batch %d' % batch_size)
        for input in onnx_model.graph.input:
            if input.type.tensor_type.shape.dim[0].dim_value == 0:
                input.type.tensor_type.shape.dim[0].dim_value = batch_size
            else:
                logging.info(
                    'input %s batch has exist %d' % (input.name, input.type.tensor_type.shape.dim[0].dim_value))

    onnx.save_model(onnx_model, output_path)
    logging.info('Number of nodes in output ONNX file: %s, output path: %s' %
                 (len(onnx_model.graph.node), output_path))


if __name__ == '__main__':
    logging.basicConfig(
        format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
        level=logging.INFO, stream=sys.stdout, force=True
    )

    input_pb_file = sys.argv[1]
    output_onnx_file = sys.argv[2]
    batch_size = int(sys.argv[3])
    input_target = [target for target in sys.argv[4].split(';')]
    predict_target = [target for target in sys.argv[5].split(';')]
    engine_type = sys.argv[6]
    opset_version = int(sys.argv[7])
    use_evart_tool = sys.argv[8] == "True"
    export_to_onnx(input_pb_file, output_onnx_file, batch_size, input_target, predict_target, opset_version,
                   use_evart_tool)
