{"code": "NumerousTensorRT", "pluginType": "framework", "frameworkPluginDefined": {"framework": "pytorch", "config": {"frameworkId": "Pytorch1.1_Framework", "image": "mirrors.tencent.com/todacc/venus-c-lukuoliu-cuda11.4-cudnn8.7.0-trt8.6.1:0.1.8", "pyEnv": "/data/miniconda3/envs/env-3.8.8/bin/python3"}, "instanceType": "batch"}, "model": {"configGroups": {"default": {"label": "模型配置"}, "runConfig": {"label": "运行配置"}, "boreConfig": {"label": "资源组"}}, "config": {"modelName": {"uiType": "input", "required": true, "label": "无量原始模型名称"}, "modelVersion": {"uiType": "input", "required": false, "label": "转换模型版本", "defaultTipInfo": "留空则默认转换最新版本"}, "outputModelName": {"uiType": "input", "required": true, "label": "转换后模型名称"}, "predictTarget": {"uiType": "input", "required": false, "label": "预测目标", "description": "留空则默认用custom_warmup_list", "defaultTipInfo": "分号或逗号间隔，样例：pfb,lcr,y_pcr,y_real_click"}, "keepVersion": {"uiType": "int-input", "required": true, "label": "保存版本数", "description": "venus模型管理配置", "min": 1, "defaultValue": 5}, "batchSize": {"uiType": "input", "required": false, "label": "batch size 分桶列表", "defaultTipInfo": "分号或逗号间隔，样例：500,1000,1500"}, "placeholderReplace": {"uiType": "input", "required": false, "label": "CPU Target 的节点名", "defaultTipInfo": "分号或逗号间隔，样例：concat_seq,concat_nn_stat"}, "tips1": {"uiType": "tips", "message": "转换组件 v7.0.0 及后续版本，需要使用无量服务 gpu_v2.0.0 版本。", "fontColor": "#ff9800", "groupName": "runConfig"}, "engine_type": {"uiType": "select", "required": true, "label": "转换引擎", "defaultValue": "trt", "defaultTipInfo": "无量支持TensorRT和EvaRT两种推理引擎", "options": [{"label": "TensorRT", "value": "trt"}, {"label": "EvaRT", "value": "e<PERSON>t"}], "outputType": "string", "groupName": "runConfig"}, "fp16": {"uiType": "switch", "required": false, "label": "开启 FP16 量化", "groupName": "runConfig"}, "use_refit": {"uiType": "switch", "required": false, "defaultValue": true, "label": "开启 Refit", ":show": "veui_context.engine_type === 'trt'", "groupName": "runConfig"}, "paramsPairs": {"uiType": "textarea", "required": false, "label": "高级参数配置", "groupName": "runConfig"}, "cephfs": {"uiType": "resource-select", "required": false, "label": "CephFS资源", "resourceType": "CephFs", "groupName": "boreConfig"}, "boreConfig": {"uiType": "bore-config", "boreTaskType": "pytorch", "includeMirror": false, "computeResourceMode": "gpu", "runMode": "single", "groupName": "boreConfig"}}}}