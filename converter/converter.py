from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import TypeVar, Generic

# 定义配置类型
ConfigType = TypeVar('ConfigType', bound='ConverterConfig')


class Converter(ABC, Generic[ConfigType]):
    def __init__(self, config: ConfigType):
        if not isinstance(config, ConverterConfig):
            raise TypeError(f"config must be an instance of ConverterConfig, got {type(config)}")
        self.config = config

    @abstractmethod
    def convert(self):
        pass


@dataclass
class ConverterConfig:
    origin_model_name: str = None
    origin_model_version: str = None
    output_model_name: str = None
    model_path: str = None
