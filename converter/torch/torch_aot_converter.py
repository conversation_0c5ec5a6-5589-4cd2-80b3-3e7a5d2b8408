#!/usr/bin/env python
#
# -*- coding: UTF-8 -*-
#

import importlib
import logging
import os
import sys
import zipfile
import torch
from converter.torch import dump_meta_pb2
from converter.converter import Converter, ConverterConfig


class TorchAotConverter(Converter):
    def __init__(self, config: ConverterConfig):
        super().__init__(config)

    def convert(self):
        model_path = self.config.model_path
        origin_model = self.config.origin_model_name
        origin_version = self.config.origin_model_version

        export_dir = os.path.join(model_path, "dense", "export")
        aot_dir = os.path.join(model_path, "dense", "aot")
        triton_ops_dir = os.path.join(model_path, "dense", "triton_ops")
        pb_file = os.path.join(export_dir, "aot_graph.pb")

        graph = dump_meta_pb2.Graph()
        with open(pb_file, "rb") as f:
            graph.ParseFromString(f.read())

        # 加载triton算子
        if graph.triton_ops:
            if triton_ops_dir not in sys.path:
                sys.path.insert(0, triton_ops_dir)
            for op in graph.triton_ops:
                with zipfile.ZipFile(os.path.join(model_path, op), "r") as zipf:
                    zipf.extractall(triton_ops_dir)
                op_name = os.path.basename(op).split(".zip")[0]
                importlib.import_module(op_name)

        for sub_graphs in graph.sub_graphs:
            file_name = os.path.join(model_path, sub_graphs.file_name)
            target_file = os.path.join(aot_dir, os.path.basename(sub_graphs.file_name))

            logging.info(f"try aoti compile {file_name}")

            exported_program = torch.export.load(file_name)
            torch._inductor.aoti_compile_and_package(exported_program, package_path=target_file)
            logging.info(f"aoti compile {sub_graphs.name} success")

            inputs = []
            for input in sub_graphs.inputs:
                inputs.append(torch.load(os.path.join(model_path, input.example_file)))

            aot_model = torch._inductor.aoti_load_package(target_file)
            if sub_graphs.kwargs_file_name != "":
                kwargs = torch.load(os.path.join(model_path, sub_graphs.kwargs_file_name))
                aot_output = aot_model(*inputs, **kwargs)
            else:
                aot_output = aot_model(*inputs)

            logging.warning(f"aot output: {aot_output}")

            outputs = []
            for output in sub_graphs.outputs:
                outputs.append(torch.load(os.path.join(model_path, output.example_file)))
            logging.warning(f"origin outputs: {outputs}")

        logging.info(f"convert {origin_model}/{origin_version} success")
