# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: dump_meta.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0f\x64ump_meta.proto\x12\x08numerous\"N\n\nTensorMeta\x12\x0c\n\x04name\x18\x01 \x01(\t\x12%\n\tdata_type\x18\x02 \x01(\x0e\x32\x12.numerous.DataType\x12\x0b\n\x03\x64im\x18\x03 \x03(\x05\"V\n\tParamMeta\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x14\n\x0c\x65xample_file\x18\x02 \x01(\t\x12%\n\x07tensors\x18\x03 \x03(\x0b\x32\x14.numerous.TensorMeta\"\xae\x01\n\x08SubGraph\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12#\n\x06inputs\x18\x03 \x03(\x0b\x32\x13.numerous.ParamMeta\x12$\n\x07outputs\x18\x04 \x03(\x0b\x32\x13.numerous.ParamMeta\x12\r\n\x05prevs\x18\x05 \x03(\t\x12\r\n\x05nexts\x18\x06 \x03(\t\x12\x18\n\x10kwargs_file_name\x18\x07 \x01(\t\"C\n\x05Graph\x12&\n\nsub_graphs\x18\x01 \x03(\x0b\x32\x12.numerous.SubGraph\x12\x12\n\ntriton_ops\x18\x02 \x03(\t*\xa3\x01\n\x08\x44\x61taType\x12\x0b\n\x07\x66loat16\x10\x00\x12\x0c\n\x08\x62\x66loat16\x10\x01\x12\x0b\n\x07\x66loat32\x10\x02\x12\x0b\n\x07\x66loat64\x10\x03\x12\x08\n\x04int8\x10\x04\x12\t\n\x05uint8\x10\x05\x12\t\n\x05int16\x10\x06\x12\n\n\x06uint16\x10\x07\x12\t\n\x05int32\x10\x08\x12\n\n\x06uint32\x10\t\x12\t\n\x05int64\x10\n\x12\n\n\x06uint64\x10\x0b\x12\x08\n\x04\x62ool\x10\x0c\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'dump_meta_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _DATATYPE._serialized_start=444
  _DATATYPE._serialized_end=607
  _TENSORMETA._serialized_start=29
  _TENSORMETA._serialized_end=107
  _PARAMMETA._serialized_start=109
  _PARAMMETA._serialized_end=195
  _SUBGRAPH._serialized_start=198
  _SUBGRAPH._serialized_end=372
  _GRAPH._serialized_start=374
  _GRAPH._serialized_end=441
# @@protoc_insertion_point(module_scope)
