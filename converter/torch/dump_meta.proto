syntax = "proto3";
package numerous;

enum DataType {
  float16 = 0;
  bfloat16 = 1;
  float32 = 2;
  float64 = 3;
  int8 = 4;
  uint8 = 5;
  int16 = 6;
  uint16 = 7;
  int32 = 8;
  uint32 = 9;
  int64 = 10;
  uint64 = 11;
  bool = 12;
}


message TensorMeta {
  string name = 1;
  DataType data_type = 2;
  repeated int32 dim = 3;
}

message ParamMeta {
  string name = 1;
  string example_file = 2;
  repeated TensorMeta tensors = 3;
}

message SubGraph {
  string name = 1;
  string file_name = 2;
  repeated ParamMeta inputs = 3;
  repeated ParamMeta outputs = 4;
  repeated string prevs = 5;
  repeated string nexts = 6;
  string kwargs_file_name = 7;
}

message Graph {
  repeated SubGraph sub_graphs = 1;
  string triton_ops = 2;
}
