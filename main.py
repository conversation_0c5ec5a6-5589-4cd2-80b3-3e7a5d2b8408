import json
import logging
import os
import re
import shutil
import time
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
import multiprocessing
from typing import List, Optional

from components import cos, register_model, const, model_utils
from components.convert_manager import update_convert_manager_progress
from components.report_duration import report_duration
from components.task_record import create_task_record, update_task_record, is_repeat_convert
from evart import evart_engine, make_config_json
from model_export import modify_model_meta
from trt import trt_engine
from converter.converter import ConverterConfig


def _build_single_trt_engine_worker(args):
    """
    多进程工作函数：构建单个 batch size 的 TRT 引擎
    这个函数必须在模块级别定义，以便可以被 pickle 序列化
    """
    (version, batch_size, output_path, item_batch_unit, is_first_engine,
     model_cache_dir, original_model_name, engine_type, opset_version,
     convert_with_refit, use_user_item_inputs, fp16, group_id) = args

    try:
        import logging
        import os
        import json
        from trt import trt_engine
        from components import model_utils, const

        logging.info(f'Process {os.getpid()}: Generating trt engine for batch size: {batch_size}...')

        # 重新创建必要的对象实例
        model_cache = model_utils.ModelCache(model_cache_dir, original_model_name)
        param_parser_instance = model_cache[version]
        graph_converter = param_parser_instance.graph_converter

        # fix ONNX file batch size
        output_onnx_file = os.path.join(model_cache_dir, version, f'model_{batch_size}.onnx')
        graph_converter.export_to_onnx(batch_size, output_onnx_file, engine_type, opset_version,
                                       convert_with_refit, use_user_item_inputs)

        # generate engine file
        output_engine_file = os.path.join(output_path, f'model_{batch_size}.engine')
        logging.info(f'Process {os.getpid()}: Build trt engine {output_engine_file} from ONNX file {output_onnx_file}...')

        # 计算 user_item_param
        user_item_param = _get_user_item_param_worker(output_onnx_file, item_batch_unit, batch_size, group_id)

        trt_engine.build_trt_engine_by_plugin(output_onnx_file, fp16, const.USE_REFIT, output_engine_file,
                                              json.dumps(user_item_param), is_first_engine)

        logging.info(f'Process {os.getpid()}: Successfully built TRT engine for batch size {batch_size}')
        return output_engine_file

    except Exception as e:
        logging.error(f'Process {os.getpid()}: Failed to build TRT engine for batch size {batch_size}: {str(e)}')
        raise


def _get_user_item_param_worker(output_onnx_file, item_batch_unit, batch_size, group_id):
    """
    多进程工作函数：获取 user_item_param
    """
    import onnx

    model = onnx.load(output_onnx_file)
    user_item_param = dict()

    for input_info in model.graph.input:
        input_name = input_info.name
        if 'user' in input_name.lower():
            user_item_param[input_name] = {'batch_unit': item_batch_unit, 'batch_size': batch_size}
        elif 'item' in input_name.lower():
            user_item_param[input_name] = {'batch_unit': item_batch_unit, 'batch_size': batch_size}

    return user_item_param


class Routine(object):
    def __init__(self, group_id: int, job_id: int, session_id: str, appinstance_name: str, original_model_name: str,
                 model_version: str, output_model_name: str, predict_target: str, keep_version: int, cos_path: str,
                 batch_size: str, placeholder_replace: str, engine_type: str, fp16: bool, use_refit: bool,
                 is_register: bool, resource_group_name: str, params_pairs: str, opset_version: int, user_inputs: str,
                 item_inputs: str, convert_with_refit: bool, force_convert_with_refit: bool):
        self.model_copy = None
        self.convert_time_s = time.time()
        self.group_id = group_id
        self.job_id = job_id
        self.session_id = session_id
        self.appinstance_name = appinstance_name
        self.original_model_name = original_model_name

        if model_version == '':
            self.model_version = None
        else:
            self.model_version = model_version
        self.output_model_name = output_model_name

        # 用于更新 model.meta 中的模型转换配置
        # 记录实际转换的 predict_target
        # 优先使用用户输入的predict_target，如果为空，再使用模型conf文件中的custom_warmup_list
        self.convert_predict_targets: List[str] = list()
        if predict_target == '':
            self.predict_target = None
        else:
            self.predict_target = [target for target in re.split(';|,', predict_target)]
            self.convert_predict_targets = self.predict_target

        self.keep_version = keep_version
        self.cos_path = cos_path
        self.resource_group_name = resource_group_name
        self.params_pairs = params_pairs
        self.user_inputs = user_inputs
        self.item_inputs = item_inputs
        self.use_user_item_inputs = False
        if self.user_inputs != '' and self.item_inputs != '':
            self.use_user_item_inputs = True

        if batch_size == '':
            self.batch_size_list = None
        else:
            self.batch_size_list = [int(batch) for batch in re.split(';|,', batch_size)]

        if placeholder_replace == '':
            self.placeholder_replace = []
        else:
            self.placeholder_replace = re.split(';|,', placeholder_replace)

        self.fp16 = fp16
        self.opset_version = opset_version

        # use EvaRT or TensorRT
        self.engine_type = engine_type

        # set const
        if not use_refit:
            const.USE_REFIT = False
        if fp16:
            const.ENGINE_PRECISION = "FP16"
        const.ORI_MODEL_NAME = original_model_name

        # register switch
        self.is_register = is_register

        self.original_model_info: Optional[model_utils.ModelInfo] = None
        self.output_model_info: Optional[model_utils.ModelInfo] = None
        self.model_cache: Optional[model_utils.ModelCache] = None
        self.model_conf_id: Optional[int] = None
        self.graph_converter = None
        self.task_id = 0

        # 是否通过refit转模
        self.convert_with_refit = convert_with_refit
        self.force_convert_with_refit = force_convert_with_refit
        # 记录当前refit的版本
        self.refit_version: Optional[str] = None

    def main(self):
        logging.info("start routine...")
        self.original_model_info = model_utils.ModelInfo(self.group_id, self.original_model_name)
        self.output_model_info = model_utils.ModelInfo(self.group_id, self.output_model_name)
        self.model_cache = model_utils.ModelCache(self.original_model_name)

        if self.model_version is not None:
            # run on specific model version
            self.run_once(self.model_version)
        else:
            # run on latest model version
            self.run_once()

    def __get_convert_model_version(self) -> str:
        """
        获取模型版本号
        """
        original_model_versions = self.original_model_info.model_versions
        # Judging the existence of the original model
        if len(original_model_versions) < 1:
            raise Exception('Original model instance not found...')

        model_version = original_model_versions[-1]
        logging.info('Convert model version: %s' % model_version)
        return model_version

    def __is_model_converted(self, model_version: str) -> bool:
        """
        判断模型是否已转换过
        """
        output_model_versions = self.output_model_info.model_versions
        if model_version in output_model_versions:
            logging.info(f'Model already converted')
            return True
        return False

    def get_last_version(self, version: str):
        cached_versions = self.model_cache.get_all_versions()
        for last_version in cached_versions:
            if last_version == version or not self.model_cache[last_version].param_parsed:
                continue

            latest_version_graph_file = os.path.join(self.model_cache[version].model_path,
                                                     self.model_cache[version].model_slice['dense'].graph_file)
            last_version_graph_file = os.path.join(self.model_cache[last_version].model_path,
                                                   self.model_cache[last_version].model_slice['dense'].graph_file)

            if model_utils.calc_sha1sum(latest_version_graph_file) == model_utils.calc_sha1sum(last_version_graph_file):
                logging.info('Graph file %s and %s are the same, use version %s to generate engine' %
                             (last_version_graph_file, latest_version_graph_file, version))
                return last_version

        return None

    def get_latest_converted_model_version(self):
        output_model_versions = self.output_model_info.model_versions
        # Judging the existence of the output model
        if len(output_model_versions) < 1:
            logging.info('output model versions is None, need generate')
            return None

        latest_converted_version = output_model_versions[-1]
        logging.info('Latest converted model version: %s' % latest_converted_version)

        return latest_converted_version

    def get_onnx_path(self, version: str, batch_size: Optional[int] = None):
        output_dir = self.model_cache[version].model_path
        if batch_size is None:
            return os.path.join(output_dir, '%s.onnx' % version)
        else:
            return os.path.join(output_dir, '%s_%d.onnx' % (version, batch_size))

    def get_refit_onnx_path(self, refit_version: str, batch_size: Optional[int] = None):
        output_dir = self.model_cache[refit_version].model_path
        if batch_size is None:
            return os.path.join(output_dir, 'refit.onnx')
        else:
            return os.path.join(output_dir, '%d_refit.onnx' % batch_size)

    def get_nobatch_onnx_path(self, version: str):
        output_dir = self.model_cache[version].model_path
        return os.path.join(output_dir, '%s_nobatch.onnx' % version)

    def convert_graph(self, version: str):
        """
        读取predict target，创建TFGraphConverter对象，生成frozen graph
        将TFGraphConverter对象写入self.converter成员变量，后续导出ONNX时还需要使用
        """

        # 延迟import,pytorch类型的镜像会找不到tensorflow相关的包报错
        from graph_converter import graph_converter

        # 读取predict target
        # 优先使用用户输入的predict_target，如果为空，再使用custom_warmup_list
        predict_target = self.predict_target
        if predict_target is None:
            predict_target = self.model_cache[version].predict_target
            self.convert_predict_targets = predict_target
            logging.info('Set predict target to %s' % str(predict_target))

        self.graph_converter = graph_converter.TFGraphConverter(
            self.model_cache[version], predict_target, self.placeholder_replace, self.batch_size_list)
        self.graph_converter.clean_and_freeze_graph(self.model_cache[version].model_path)

    def generate_engine_by_evart_tool(self, version: str):
        """
        1. 调用 self.converter.export_to_onnx 生成配置内的第一个 batch size 的ONNX文件
        如果 engine_type == 'trt'
            1. 调用 evart_engine.build_trt_engine 构建包含业务设置的所有 batch size 的多个 batch size 的 trt engine
            2. 第一个 trt engine 会校验推理结果
        否则，构建 evart engine:
            1. 调用 evart_engine.build_evart_engine 构建包含业务设置的所有 batch size 的一个 evart engine
            2. 构建第一个 engine 时会校验推理结果（第一个batch size）
        """

        output_path = self.model_cache[version].model_path
        logging.info('Engine type is %s , batch size: %s ...' % (self.engine_type, self.batch_size_list))

        output_onnx_file = self.get_onnx_path(version)

        if self.engine_type == 'trt':
            logging.info('Generating trt engine for batch size: %s...' % self.batch_size_list)

            # create a trt config file with onnx file
            trt_config_file = os.path.abspath(
                os.path.join(os.path.dirname(output_onnx_file), const.TRT_CONFIG_FILE_NAME))
            make_config_json.write_config_file(trt_config_file, output_onnx_file, self.fp16, self.batch_size_list,
                                               self.engine_type, self.opset_version)
            logging.info('Create TRT config file %s...', trt_config_file)

            # generate engine file, eg: model_50.engine, model_100.engine
            output_engine_file = os.path.abspath(output_onnx_file.rstrip("onnx") + "engine")
            logging.info('Build trt engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
            # 默认开启 trt 测试
            test_inference = True
            trt_engine.build_trt_engine(output_onnx_file, output_engine_file, trt_config_file, self.batch_size_list,
                                        test_inference)

            engine_count = 0
            engine_file_list = list()
            for batch_size in self.batch_size_list:
                # generate engine file
                # serving load engine file depends on the engine file name format,
                # if changing it here, need to change the serving load logic as well
                engine_file = os.path.join(output_path, 'model_%d.engine' % batch_size)

                engine_count += 1
                engine_file_list.append(engine_file)

            logging.info('trt engine generated, %d files' % engine_count)

            output_engine_files = ';'.join(engine_file_list)
            logging.info('Build trt engine %s from ONNX file %s...' % (output_engine_files, output_onnx_file))

            logging.info('Check all trt Engine: %s ' % engine_file_list)
            trt_engine.infer_trt_engine(engine_file_list)

        else:
            # engine_type == 'evart'
            logging.info('Generating evart engine for batch size: %s...' % self.batch_size_list)

            # create a evart config file with onnx_file
            eva_config_file = os.path.abspath(
                os.path.join(os.path.dirname(output_onnx_file), const.EVART_CONFIG_FILE_NAME))
            make_config_json.write_config_file(eva_config_file, output_onnx_file, self.fp16, self.batch_size_list,
                                               self.engine_type, self.opset_version)
            logging.info('Create EvaRT config file %s...', eva_config_file)

            # generate engine file
            output_engine_file = os.path.abspath(output_onnx_file.rstrip("onnx") + "evart")
            logging.info('Build evart engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
            # 默认开启 evart测试
            test_inference = True
            evart_engine.build_evart_engine(output_onnx_file, output_engine_file, eva_config_file, \
                                            self.batch_size_list, self.fp16, test_inference)

    def get_user_item_param(self, onnx_file: str, item_batch_unit: int, item_batch_size: int):
        from onnx_utils.read_onnx import get_inputs_shape

        user_item_param = {}
        if not self.use_user_item_inputs:
            return user_item_param
        # item_batch 需要为基础batch的整数倍
        if item_batch_size % item_batch_unit != 0:
            raise Exception('Batch size must be an integer multiple of the base batch: %d' % item_batch_unit)
        user_batch_size = item_batch_size // item_batch_unit
        user_inputs = [f"{node}:0" for node in self.user_inputs.split(',')]
        item_inputs = [f"{node}:0" for node in self.item_inputs.split(',')]
        inputs_shape = get_inputs_shape(onnx_file, user_inputs + item_inputs)
        for user_input in user_inputs:
            if user_input not in inputs_shape:
                raise Exception('Input not found: %s' % user_input)
            inputs_shape[user_input][0] = user_batch_size
            user_item_param[user_input] = inputs_shape[user_input]
        for item_input in item_inputs:
            if item_input not in inputs_shape:
                raise Exception('Input not found: %s' % item_input)
            inputs_shape[item_input][0] = item_batch_size
            user_item_param[item_input] = inputs_shape[item_input]
        return user_item_param

    def generate_engine(self, version: str):
        """
        不走evart工具，组件直接构建引擎
        如果配置中使用refit并且可以 refit 则通过 refit 构建引擎，加速引擎的生成
        否则，重新构建 引擎
        """

        if self.convert_with_refit:
            refit_version = self.check_refit_engine(version)
            logging.info('Converting model version: %s , using refit version: %s' % (version, refit_version))
            if refit_version is None:
                self.convert_engine_for_refit(version)
                # 更新 refit 版本
                # 第一次转换成功后, 后续可能一次转换多个版本, 使用第一次转换的版本作为 refit 版本
                self.update_refit_version(version)
            else:
                self.convert_engine_use_refit(version, refit_version)
        else:
            self.convert_engine(version)

    def _build_trt_engines_parallel(self, version: str, output_path: str):
        """
        并行构建多个 batch size 的 TRT 引擎
        使用多进程池实现真正的并行处理
        """
        item_batch_unit = self.batch_size_list[0]
        engine_file_list = []

        # 准备任务参数
        tasks = []
        for i, batch_size in enumerate(self.batch_size_list):
            is_first_engine = (i == 0)  # 只有第一个引擎需要进行推理验证
            task_args = (
                version, batch_size, output_path, item_batch_unit, is_first_engine,
                self.model_cache[version].model_cache_dir, self.original_model_name, self.engine_type,
                self.opset_version, self.convert_with_refit, self.use_user_item_inputs,
                self.fp16, self.group_id
            )
            tasks.append(task_args)

        # 使用多进程池并行处理
        max_workers = min(len(self.batch_size_list), multiprocessing.cpu_count(), 2)  # 限制最大进程数
        logging.info(f'Using {max_workers} processes to build {len(self.batch_size_list)} TRT engines in parallel')

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_batch = {}
            for i, task_args in enumerate(tasks):
                batch_size = task_args[1]  # batch_size 是第二个参数
                future = executor.submit(_build_single_trt_engine_worker, task_args)
                future_to_batch[future] = batch_size

            # 收集结果
            for future in as_completed(future_to_batch):
                batch_size = future_to_batch[future]
                try:
                    engine_file = future.result()
                    engine_file_list.append(engine_file)
                    logging.info(f'Completed TRT engine for batch size {batch_size}')
                except Exception as e:
                    logging.error(f'Failed to build TRT engine for batch size {batch_size}: {str(e)}')
                    raise

        # 按照 batch_size 排序，保持一致性
        engine_file_list.sort(key=lambda x: int(re.search(r'model_(\d+)\.engine', x).group(1)))
        return engine_file_list

    def convert_engine(self, version: str):
        """
        组件自己构建 engine
        如果engine_type == 'trt', 构建trt engine:
            为业务设置的每一个batch size：
                1. 调用self.converter.export_to_onnx生成batch size大小固定的ONNX文件
                2. 调用trt_engine.build_trt_engine构建trt engine
                3. 构建第一个engine时会校验推理结果
        否则，构建evart engine:
                1. 调用self.converter.export_to_onnx生成配置内的第一个batch size的ONNX文件
                2. 调用evart_engine.build_evart_engine构建包含业务设置的所有batch size的一个evart engine
                3. 构建第一个engine时会校验推理结果（第一个batch size）
        """

        output_path = self.model_cache[version].model_path
        logging.info('Engine type is %s ...' % self.engine_type)
        if self.engine_type == 'trt':
            engine_file_list = self._build_trt_engines_parallel(version, output_path)
            logging.info('Engine generated, %d files' % len(engine_file_list))

            logging.info('Check all Engine: %s ' % engine_file_list)
            trt_engine.infer_trt_engine(engine_file_list)

        else:
            # engine_type == 'evart'
            logging.info('Generating evart engine for batch size: %s...' % self.batch_size_list)

            # fix ONNX file batch size
            output_onnx_file = self.get_onnx_path(version)
            batch_size = self.batch_size_list[0]
            self.graph_converter.export_to_onnx(batch_size, output_onnx_file, self.engine_type, self.opset_version,
                                                self.convert_with_refit, self.use_user_item_inputs)

            # create a evart config file with onnx_file
            eva_config_file = os.path.abspath(os.path.join(os.path.dirname(output_onnx_file), "evart_config.json"))
            make_config_json.write_config_file(eva_config_file, output_onnx_file, self.fp16, self.batch_size_list,
                                               self.engine_type, self.opset_version)
            logging.info('Create EvaRT config file %s...', eva_config_file)

            # generate engine file
            output_engine_file = os.path.abspath(output_onnx_file.rstrip("onnx") + "evart")
            logging.info('Build evart engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
            # 默认开启 evart测试
            test_inference = True
            evart_engine.build_evart_engine(output_onnx_file, output_engine_file, eva_config_file, \
                                            self.batch_size_list, self.fp16, test_inference)

    def convert_engine_for_refit(self, version: str):
        """
        组件构建 engine
        如果engine_type == 'trt', 构建trt engine:
                1. 调用self.converter.export_to_onnx生成batch size大小不固定的ONNX文件（不设置 pb 文件的 batch size）
                2. 遍历所有 batch size，更新 onnx 的 batch size
                3. 调用trt_engine.build_trt_engine构建trt engine
                4. 构建第一个engine时会校验推理结果
                NOTE: 部分模型，pb 先设置 batchsize 转 onnx 和 不设置 batchsize 转 onnx 得到的图结构不同，这种方式将不适用于这样的模型
        否则，构建evart engine:
                1. 调用self.converter.export_to_onnx生成配置内的第一个batch size的ONNX文件
                2. 调用evart_engine.build_evart_engine构建包含业务设置的所有batch size的一个evart engine
                3. 构建第一个engine时会校验推理结果（第一个batch size）
        """

        output_path = self.model_cache[version].model_path
        logging.info('Convert engine with refit, engine type is %s' % self.engine_type)
        if self.engine_type == 'trt':
            # 先不设置 pb 的 batch size, 先转出 batchsize 不固定的 onnx，转对应 batch 的 trt engine 时再设置 onnx 文件的 batch
            no_batch_onnx_file = self.get_nobatch_onnx_path(version)
            batch_size = self.batch_size_list[0]
            self.graph_converter.export_to_onnx(batch_size, no_batch_onnx_file, self.engine_type, self.opset_version,
                                                self.convert_with_refit, self.use_user_item_inputs)

            engine_count = 0
            engine_file_list = list()
            item_batch_unit = self.batch_size_list[0]
            for batch_size in self.batch_size_list:
                logging.info('Generating trt engine for batch size: %d' % batch_size)

                # set ONNX file batch size
                output_onnx_file = self.get_onnx_path(version, batch_size)
                self.graph_converter.set_onnx_batch_size(batch_size, no_batch_onnx_file, output_onnx_file)

                # generate engine file
                # serving load engine file depends on the engine file name format,
                # if changing it here, need to change the serving load logic as well
                output_engine_file = os.path.join(output_path, 'model_%d.engine' % batch_size)
                logging.info('Build trt engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
                user_item_param = self.get_user_item_param(output_onnx_file, item_batch_unit, batch_size)
                trt_engine.build_trt_engine_by_plugin(output_onnx_file, self.fp16, const.USE_REFIT, output_engine_file,
                                                      json.dumps(user_item_param), engine_count == 0)
                engine_count += 1
                engine_file_list.append(output_engine_file)

            logging.info('Engine generated, %d files' % engine_count)

            logging.info('Check all Engine: %s ' % engine_file_list)
            trt_engine.infer_trt_engine(engine_file_list)

            # 转换完之后，拷贝 no_batch.onnx 文件为 refit.onnx 文件，后续上传到 cos，供 refit 使用
            refit_onnx_file = os.path.join(output_path, const.ONNX_FILENAME_SUFFIX)
            logging.info('Copy from onnx file: %s to refit onnx file: %s' % (no_batch_onnx_file, refit_onnx_file))
            shutil.copy(no_batch_onnx_file, refit_onnx_file)
        else:
            # engine_type == 'evart'
            logging.info('Generating evart engine for batch size: %s...' % self.batch_size_list)

            # set ONNX file batch size
            output_onnx_file = self.get_onnx_path(version)
            batch_size = self.batch_size_list[0]
            self.graph_converter.export_to_onnx(batch_size, output_onnx_file, self.engine_type, self.opset_version,
                                                self.convert_with_refit, self.use_user_item_inputs)

            # create a evart config file with onnx_file
            eva_config_file = os.path.abspath(os.path.join(os.path.dirname(output_onnx_file), "evart_config.json"))
            make_config_json.write_config_file(eva_config_file, output_onnx_file, self.fp16, self.batch_size_list,
                                               self.engine_type, self.opset_version)
            logging.info('Create EvaRT config file %s...', eva_config_file)

            # generate engine file
            output_engine_file = os.path.abspath(output_onnx_file.rstrip("onnx") + "evart")
            logging.info('Build evart engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
            # 默认开启 evart测试
            test_inference = True
            evart_engine.build_evart_engine(output_onnx_file, output_engine_file, eva_config_file, \
                                            self.batch_size_list, self.fp16, test_inference)

    def convert_engine_use_refit(self, version: str, exist_version: str):
        original_dir = self.model_cache[exist_version].model_path
        output_dir = self.model_cache[version].model_path

        logging.info('Engine type is %s ...' % self.engine_type)
        if self.engine_type == 'trt':
            # 转一次 onnx，用来获取权重
            no_batch_onnx_file = self.get_nobatch_onnx_path(version)
            batch_size = self.batch_size_list[0]
            self.graph_converter.export_to_onnx(batch_size, no_batch_onnx_file, self.engine_type, self.opset_version,
                                                self.convert_with_refit, self.use_user_item_inputs)

            # 获取上个版本的 onnx 文件，refit 时使用
            original_onnx_file = self.get_refit_onnx_path(exist_version)

            engine_count = 0
            for engine_file in os.listdir(original_dir):
                if engine_file.endswith('.engine'):
                    match = re.findall('model_(\\d+).engine', engine_file)
                    batch_size = int(match[0])
                    logging.info('Refit engine file: %s, onnx file: %s' % (engine_file, original_onnx_file))

                    # export to onnx
                    output_onnx_file = self.get_onnx_path(version, batch_size)
                    self.graph_converter.set_onnx_batch_size(batch_size, no_batch_onnx_file, output_onnx_file)

                    # refit trt engine
                    trt_engine.refit_trt_engine(os.path.join(original_dir, engine_file),
                                                original_onnx_file,
                                                output_onnx_file,
                                                os.path.join(output_dir, engine_file),
                                                engine_count == 0)
                    engine_count += 1

            logging.info('Refit %d engines...' % engine_count)
            assert engine_count > 0, 'Engine not exist'

            # refit 成功后，拷贝上个版本的 refit.onnx 文件到新版本的目录下，后续上传到 cos，供下次 refit 使用
            refit_onnx_file = os.path.join(output_dir, const.ONNX_FILENAME_SUFFIX)
            logging.info('Copy refit onnx file from %s to %s' % (original_onnx_file, refit_onnx_file))
            shutil.copy(original_onnx_file, refit_onnx_file)

        else:
            # engine_type == 'evart'
            logging.info('Update evart engine for batch size: %s...' % self.batch_size_list)

            # fix ONNX file batch size
            output_onnx_file = self.get_onnx_path(version)
            batch_size = self.batch_size_list[0]
            self.graph_converter.export_to_onnx(batch_size, output_onnx_file, self.engine_type, self.opset_version,
                                                self.convert_with_refit, self.use_user_item_inputs)

            # create a evart config file with onnx_file
            eva_config_file = os.path.abspath(os.path.join(os.path.dirname(output_onnx_file), "evart_config.json"))
            make_config_json.write_config_file(eva_config_file, output_onnx_file, self.fp16, self.batch_size_list)
            logging.info('Create EvaRT config file %s...', eva_config_file)

            # generate engine file
            output_engine_file = os.path.abspath(output_onnx_file.rstrip("onnx") + "evart")
            logging.info('Update evart engine %s from ONNX file %s...' % (output_engine_file, output_onnx_file))
            # 默认开启 evart测试
            test_inference = True
            evart_engine.update_evart_engine(output_onnx_file, output_engine_file, eva_config_file,
                                             self.batch_size_list, self.fp16, test_inference)

    def upload_model(self, version: str):
        cos_config, register_cos_config = register_model.get_copy_storage_info(self.original_model_name, version,
                                                                               self.output_model_name, version)
        cos_manager = cos.COSManager(cos_config)
        cos_manager.init_client()

        # copy sparse and dense weight files
        cos_copy_filelist = list()

        parser = self.model_cache[version]
        original_model_instance = self.original_model_info.model_instances[version]
        original_cos_path = os.path.dirname(original_model_instance.model_meta_path)
        output_cos_path = os.path.join(self.cos_path, version)
        for slice_def in parser.model_slice.values():
            if self.engine_type == "pytorch" and slice_def == "dense":
                continue
            for filename in slice_def.file_list:
                if filename != 'model.meta':
                    cos_copy_filelist.append(filename)

        logging.info('copy file count: %d' % len(cos_copy_filelist))
        self.model_copy = model_utils.ModelProcess(original_cos_path, output_cos_path, cos_manager,
                                                   const.ACTION_TYPE_COPY)
        result = self.model_copy.run_multi_processes(cos_copy_filelist)
        logging.info('asyncio result count: %d' % len(result))
        assert len(result) == len(cos_copy_filelist), 'The number of copied files is inconsistent!'

        # upload tensorRT engine files
        output_path = self.model_cache[version].model_path
        cos_upload_filelist = list()

        if self.engine_type == 'pytorch':
            for root, _, files in os.walk(os.path.join(output_path, "dense")):
                for file in files:
                    if not os.path.isfile(os.path.join(root, file)):
                        continue
                    relative_path = os.path.relpath(os.path.join(root, file), output_path)
                    cos_upload_filelist.append(relative_path)
        else:
            if self.engine_type == 'trt':
                file_end_type = const.TRT_ENGINE_FILENAME_SUFFIX
            else:
                file_end_type = const.EVART_ENGINE_FILENAME_SUFFIX
            for file_name in os.listdir(output_path):
                if file_name.endswith(file_end_type):
                    cos_upload_filelist.append(file_name)
                # 上传 refit.onnx 文件
                if file_name.endswith(const.ONNX_FILENAME_SUFFIX):
                    cos_upload_filelist.append(file_name)

        logging.info('upload file count: %d' % len(cos_upload_filelist))
        self.model_upload = model_utils.ModelProcess(output_path, output_cos_path, cos_manager,
                                                     const.ACTION_TYPE_UPLOAD)
        result = self.model_upload.run_multi_processes(cos_upload_filelist)
        logging.info('asyncio upload result count: %d' % len(result))
        assert len(result) == len(cos_upload_filelist), 'The number of upload files is inconsistent!'

        # modify model.meta file and upload
        local_model_meta_file = os.path.join(output_path, 'model.meta')
        cos_model_meta_file = os.path.join(output_cos_path, 'model.meta')
        modify_model_meta.modify_model_meta_file(local_model_meta_file, cos_upload_filelist, self.batch_size_list,
                                                 self.placeholder_replace, self.engine_type, version, self.job_id,
                                                 self.session_id, self.appinstance_name, self.opset_version,
                                                 self.convert_predict_targets)
        cos_manager.upload_single_file(local_model_meta_file, cos_model_meta_file)
        logging.info('Model meta file on COS: %s' % cos_model_meta_file)
        return cos_model_meta_file, register_cos_config

    def convert_pytorch(self, version: str):
        from converter.torch.torch_aot_converter import TorchAotConverter
        converter_config = ConverterConfig(origin_model_name=self.original_model_name,
                                           origin_model_version=version,
                                           output_model_name=self.output_model_name,
                                           model_path=self.model_cache[version].model_path)
        converter = TorchAotConverter(converter_config)
        converter.convert()

    def convert_tensorflow(self, version: str):
        from onnx_utils.read_onnx import get_unsupported_nodes_cpu_targets, get_dynamic_nodes_cpu_targets

        if not const.TRT_GENERATE_ENGINE_BY_EVART_TOOL:
            # 原逻辑：trt组件内转engine,evart工具转engine
            self.convert_graph(version)
            self.graph_converter.split_cpu_targets(self.model_cache[version].model_path)
            self.generate_engine(version)
        else:
            # 新逻辑：trt、evart都用工具转 engine
            # 计算frozen graph；获取converter，后续切分cpu_target、生成onnx使用
            self.convert_graph(version)
            retry_count = 4
            for i in range(retry_count):
                if i == retry_count - 1:
                    # 重试仍然存在不支持算子或动态维度算子，直接异常退出
                    raise Exception('unsupported nodes error')
                # 切分cpu_target
                self.graph_converter.placeholder_replace = self.placeholder_replace
                self.graph_converter.split_cpu_targets(self.model_cache[version].model_path)
                # 生成onnx
                onnx_path = self.get_onnx_path(version)
                self.graph_converter.export_to_onnx(self.batch_size_list[0], onnx_path, self.engine_type, self.opset_version,
                                                    self.convert_with_refit, self.use_user_item_inputs)
                # 如果存在不支持算子，需要更新并切分cpu_target，再重新生成onnx
                cpu_targets = get_unsupported_nodes_cpu_targets(onnx_path)
                if len(cpu_targets) != 0:
                    self.placeholder_replace = list(set(self.placeholder_replace + cpu_targets))
                    logging.info('onnx unsupported nodes retry: %s' % str(cpu_targets))
                    continue
                # 生成engine
                try:
                    self.generate_engine_by_evart_tool(version)
                except Exception as e:  # pylint: disable=broad-except
                    # 如果存在动态维度算子，需要更新并切分cpu_target，再重新生成engine
                    cpu_targets = get_dynamic_nodes_cpu_targets()
                    if len(cpu_targets) != 0:
                        self.placeholder_replace = list(set(self.placeholder_replace + cpu_targets))
                        logging.info('dynamic nodes retry: %s' % str(cpu_targets))
                        continue
                    else:
                        raise e
                # 成功生成engine，直接退出重试循环
                break

    def convert_model(self, version: str, creator: str):
        logging.info('Start converting model version %s...' % version)
        # 新建转模任务记录
        self.task_id = create_task_record(self.original_model_name, self.output_model_name, version, self.group_id,
                                          self.job_id, self.session_id, self.predict_target, self.keep_version,
                                          self.batch_size_list, self.placeholder_replace, self.engine_type, self.fp16,
                                          self.params_pairs, self.resource_group_name, self.opset_version)
        # 根据转模任务ID新建父任务耗时记录，先创建父任务耗时记录才能创建子任务耗时记录
        report_duration(self.task_id, const.CONVERT_TYPE, self.convert_time_s, self.convert_time_s)
        # 解析dense部分参数
        read_dense_time_s = time.time()
        self.model_cache[version].read_dense_parameters()
        read_dense_time_e = time.time()
        report_duration(self.task_id, const.SubTaskType.READ_DENSE.value, read_dense_time_s, read_dense_time_e)

        # 生成engine
        generate_engine_time_s = time.time()

        if self.engine_type == 'pytorch':
            self.convert_pytorch(version)
        else:
            self.convert_tensorflow(version)

        generate_engine_time_e = time.time()
        report_duration(self.task_id, const.SubTaskType.GENERATE_ENGINE.value, generate_engine_time_s,
                        generate_engine_time_e)
        logging.info('Generate engine succeed, time cost: %.3f seconds' %
                     (generate_engine_time_e - generate_engine_time_s))

        # 上传模型并注册模型实例
        if self.is_register:
            upload_time_s = time.time()
            model_meta_path, cos_config = self.upload_model(version)
            upload_time_e = time.time()
            logging.info('Upload model succeed, time cost: %.3f seconds' % (upload_time_e - upload_time_s))
            register_model.register_model_instance_storage(self.output_model_name, version,
                                                           self.model_conf_id, model_meta_path, creator, cos_config)
            report_duration(self.task_id, const.SubTaskType.REGISTER_MODEL.value, upload_time_s, upload_time_e)
            logging.info('Model version %s register succeed' % version)

        logging.info('Cleaning up cache...')
        # 更新任务状态
        runtime_data = {}
        if os.path.exists(const.EVA_RESULT_FILE_NAME):
            with open(const.EVA_RESULT_FILE_NAME, 'r') as file:
                runtime_data = json.load(file)
        runtime_data['placeholder_replace'] = self.placeholder_replace
        update_task_record(self.task_id, json.dumps(runtime_data), const.Status.SUCCESS.value)
        convert_time_e = time.time()
        report_duration(self.task_id, const.CONVERT_TYPE, self.convert_time_s, convert_time_e)
        # self.model_cache.clean()
        self.model_cache.clean(self.refit_version)

    def get_dependency_versions(self, versions: List[str]):
        dependency_versions = list()
        for version in versions:
            for dependency_version in self.model_cache[version].deps:
                self.output_model_info.list_model_instances(dependency_version)
                # 如果版本已经存在，则不需要回溯
                if dependency_version in self.output_model_info.model_instances:
                    continue
                self.original_model_info.list_model_instances(dependency_version)  # 获取依赖模型信息
                model_instance = self.original_model_info.model_instances[dependency_version]
                self.model_cache.add(dependency_version, model_instance)
                dependency_versions.append(dependency_version)

        if len(dependency_versions) == 0:
            return versions
        else:
            return versions + self.get_dependency_versions(dependency_versions)

    def run_once(self, model_version: Optional[str] = None):
        # 查询需要转模的原始模型，如果没有指定版本号，则查询原始模型最新版本
        self.original_model_info.list_model_instances(model_version)
        model_version = self.__get_convert_model_version()
        # 判断是否为增量模型
        register_model.parse_model_incremental_status(self.original_model_name, model_version)
        # 查询导出模型目的一、判断需要转模型的版本是否已经有导出模型，如果已经转好就不用再转了：
        # 指定版本号转模，且指定版本号不是最新的版本号，必须要用指定版本号查询导出模型
        # 没有指定版本号(model_version=None)，即对默认对最新版本转模，或指定的版本号是最新的版本号，也可以用model_version查询导出模型
        self.output_model_info.list_model_instances(model_version)
        # 查询导出模型目的二、通过refit转模，需要依赖之前导出的模型文件，这里要求查询最新的导出模型版本
        self.output_model_info.list_model_instances()
        # 如果模型版本已转换过，则直接返回
        if self.__is_model_converted(model_version) and self.is_register:
            return

        logging.info('Start running on version %s...' % model_version)
        model_instance = self.original_model_info.model_instances[model_version]
        self.model_cache.add(model_version, model_instance)

        if self.is_register:
            self.model_conf_id = register_model.check_and_save_model_info(self.group_id, self.original_model_name,
                                                                          self.output_model_name, self.keep_version,
                                                                          self.job_id)

        if const.INCREMENTAL:
            # 拉取模型依赖关系链
            all_versions_list = self.get_dependency_versions([model_version])
            all_versions = list(dict.fromkeys(all_versions_list).keys())
        else:
            all_versions = [model_version]
        logging.info('Dependency parse succeed, versions need to convert: %s' % (str(all_versions)))
        # 判断是否重复转模，避免重复转模导致转模失败
        if is_repeat_convert(self.output_model_name, all_versions):
            return
        # 倒序转换
        task_count = len(all_versions)
        current_task_num = 0
        for version in all_versions[::-1]:
            current_task_num += 1
            update_convert_manager_progress(self.output_model_name, self.model_version, task_count, current_task_num,
                                            version)
            try:
                self.convert_model(version, model_instance.creator)
            except Exception as e:  # pylint: disable=broad-except
                runtime_data_err_msg = json.dumps({"errMsg": str(e)})
                update_task_record(self.task_id, runtime_data_err_msg, const.Status.FAILED.value)
                raise e

        # 蓝盾检测
        logging.info('Model %s is successfully converted to Model %s by TensorRT' % (
            self.original_model_name, self.output_model_name))

    def check_refit_config(self, refit_config: list, convert_config: list):
        try:
            if len(refit_config) != len(convert_config):
                return False
            sorted_refit_config = sorted(refit_config)
            sorted_convert_config = sorted(convert_config)
            if sorted_refit_config == sorted_convert_config:
                return True
            return False
        except Exception as e:
            logging.error('Check refit config failed: %s', str(e))
            return False

    def check_refit_engine(self, version: str):
        '''
        获取上一个转换过的版本，如果存在，则检查是否可以通过 refit 更新 engine，如果可以则返回可 refit 的版本号，否则返回 None
        1. 检查是否是可 refit 的
        2. 检查上一个版本和当前待转版本的转换配置是否一样 (batch size, cpu targets, predict targets, opset version, precision)
        3. graph.pb 和 graph.pbtxt 不再做检查, gpu 训练出来的模型，pb 和 pbtxt 包含模型参数，肯定不同
        '''
        if not self.force_convert_with_refit and len(self.model_cache[version].deps) == 0:
            # 非强制走refit，且是全量模型，需要重新转，生成refit相关配置和文件
            return None
        # 获取上一个转换过的模型版本
        if self.refit_version is not None:
            # 一次转换多个版本的情况，第一个版本已经转换过，后续版本的直接使用第一次转的版本即可
            logging.info('Use local refit version: %s', self.refit_version)
            return self.refit_version
        version = self.get_latest_converted_model_version()
        if version is None:
            # 如果本地也没转换过，则需要执行转换
            logging.info('The output model has not been converted, convert it directly')
            return None

        # 模型已经转换和注册过，加入到 cache 中，并下载需要的模型文件
        model_instance = self.output_model_info.model_instances[version]
        self.model_cache.add(version, model_instance, True)

        cached_versions = self.model_cache.get_all_versions()
        if version not in cached_versions:
            logging.info('version %s not found in model cache', version)
            return None

        logging.info('There is converted version of output model, version: %s, check refittable...', version)
        # 检测模型版本中是否有 engine 和 refit.onnx 文件，如果有则再继续判断其他配置
        trt_engine_files_num = len(self.model_cache[version].model_slice['dense'].trt_engine_files)
        onnx_files_num = len(self.model_cache[version].model_slice['dense'].onnx_files)
        if trt_engine_files_num <= 0 or onnx_files_num <= 0:
            logging.info(
                'No trt engine files or onnx files of output model version: %s, engine file num: %d, onnx file num: %d'
                % (version, trt_engine_files_num, onnx_files_num))
            return None

        # 上个版本待检查的配置项
        engine_refittable = self.model_cache[version].engine_refittable
        engine_batch_size = self.model_cache[version].engine_batch_size
        engine_cpu_targets = self.model_cache[version].engine_cpu_targets
        engine_predict_targets = self.model_cache[version].engine_predict_targets
        engine_opset_version = self.model_cache[version].engine_opset_version
        engine_precision = self.model_cache[version].engine_precision

        # 检查是否可以 refit
        if not engine_refittable:
            logging.info('version %s not refittable', version)
            return None

        # 检查 batch size list 是否发生变化
        # 1.对 refit 版本和当前版本的 batchsize list 排序
        # 2.比较是否相同，如果不同则返回 None
        if not self.check_refit_config(engine_batch_size, self.batch_size_list):
            logging.info('batch size is different with refit version %s, current version is: %s, refit version is: %s'
                         % (version, self.batch_size_list, engine_batch_size))
            return None

        # 检查 cpu target 是否发生变化
        # 1.对 refit 版本和当前版本的 cpu targets 排序
        # 2.比较是否相同，如果不同则返回 None
        if not self.check_refit_config(engine_cpu_targets, self.placeholder_replace):
            logging.info(
                'placeholder replace is different with refit version %s, current version is: %s, refit version is: %s'
                % (version, self.placeholder_replace, engine_cpu_targets))
            return None

        # 检查 predict target 是否发生变化
        # 1.对 refit 版本和当前版本的 predict target 排序
        # 2.比较是否相同，如果不同则返回 None
        if not self.check_refit_config(engine_predict_targets, self.convert_predict_targets):
            logging.info(
                'predict target is different with refit version %s, current version is: %s, refit version is: %s'
                % (version, str(self.convert_predict_targets), str(engine_predict_targets)))
            return None

        if engine_opset_version != self.opset_version:
            logging.info(
                'opset version is different with refit version %s, current version is: %s, refit version is: %s'
                % (version, self.opset_version, engine_opset_version))
            return None

        if engine_precision != const.ENGINE_PRECISION:
            logging.info(
                'engine precision is different with refit version %s, current version is: %s, refit version is: %s'
                % (version, const.ENGINE_PRECISION, engine_precision))
            return None

        # graph.pb 和 graph.pbtxt 不再做检查
        # 因 gpu 训练出来的模型，pb 和 pbtxt 文件是不相同的，包含了训练的权重信息

        logging.info('Set refittable version: %s' % version)
        self.refit_version = version

        return version

    def update_refit_version(self, version: str):
        # 可能是个增量版本或者全量版本
        logging.info('Update refitting version from version %s to version %s' % (self.refit_version, version))
        self.refit_version = version

        # 更新 model cache 中的信息
        self.model_cache[version].engine_refittable = const.USE_REFIT
        self.model_cache[version].engine_batch_size = self.batch_size_list
        self.model_cache[version].engine_cpu_targets = self.placeholder_replace
        self.model_cache[version].engine_predict_targets = self.convert_predict_targets
        self.model_cache[version].engine_opset_version = self.opset_version
        self.model_cache[version].engine_precision = const.ENGINE_PRECISION


# 多进程保护，确保在 Windows 和其他平台上正常工作
if __name__ == '__main__':
    multiprocessing.set_start_method('spawn', force=True)
