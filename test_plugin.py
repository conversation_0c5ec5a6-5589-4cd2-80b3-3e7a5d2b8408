import logging
import os
import sys

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '0'
sys.path.append('./mock')

from plugin import BorePyTorchPlugin
from sdk import JobParameter, ExecutorProvider

logging.basicConfig(
    format='[%(asctime)s] %(filename)s-%(levelname)s-%(lineno)d: %(message)s',
    level=logging.INFO, stream=sys.stdout, force=True
)


def test_one_model(model: dict):
    execution_provider = ExecutorProvider()
    plugin = BorePyTorchPlugin(execution_provider)
    [model.setdefault(k, v) for k, v in
     [('f_app_group_id', '874'),
      ('f_session_id', 'SM20240416102159979_816751021'),
      ('appinstance_name', 'vc-816772916-20240530-xxkrschk'),
      ('opset_version', 13),
      ('boreConfig.resourceGroupName', '-1'),
      ('f_job_id', '816751021'),
      ('job_owner', 'benshen'),
      ('op_code', 'NumerousTensorRT'),
      ('op_version', '1.latest'),
      ('env', 'formal'),
      ('is_debug', 'True'),
      ('modelName', 'NumerousTensorRT'),
      ('modelVersion', '2024_04_25_161325'),
      ('outputModelName', 'NumerousTensorRT_trt'),
      ('is_register', False),
      ('keepVersion', '5'),
      ('predictTarget', ''),
      ('placeholderReplace', ''),
      ('batchSize', '50'),
      ('engine_type', 'trt'),# trt/evart
      ('fp16', False),
      ('use_refit', True),
      ('paramsPairs', '')]]
    job_parameter = JobParameter(model)
    job_parameter.secret = {
        'venus_openapi_sid': model['proxy_user_secret_id'],
        'venus_openapi_skey': model['proxy_user_secret_key'],
    }
    plugin.execute(job_parameter)


if __name__ == '__main__':
    # 在 https://venus.woa.com/#/openapi/accountManage/personalAccount 可以申请查看 secret_id 和 secret_key
    model = {
        'proxy_user_secret_id': '',
        'proxy_user_secret_key': '',
    }
    test_one_model(model)
