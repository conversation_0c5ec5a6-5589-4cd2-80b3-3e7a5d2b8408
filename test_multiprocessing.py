#!/usr/bin/env python
# -*- coding: UTF-8 -*-

"""
测试多进程TensorRT引擎构建功能
"""

import time
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed

# 设置日志
logging.basicConfig(
    format='[%(asctime)s] %(levelname)s: %(message)s',
    level=logging.INFO
)

def mock_build_engine(batch_size, delay=2):
    """
    模拟构建TensorRT引擎的过程
    """
    logging.info(f'开始构建batch size {batch_size}的引擎...')
    time.sleep(delay)  # 模拟构建时间
    logging.info(f'完成构建batch size {batch_size}的引擎')
    return f'model_{batch_size}.engine'

def test_multiprocessing_build():
    """
    测试多进程构建引擎
    """
    batch_sizes = [50, 100, 200]
    
    logging.info(f'开始测试多进程构建，batch sizes: {batch_sizes}')
    start_time = time.time()
    
    # 使用ProcessPoolExecutor
    with ProcessPoolExecutor(max_workers=3) as executor:
        # 提交所有任务
        future_to_batch = {}
        for batch_size in batch_sizes:
            future = executor.submit(mock_build_engine, batch_size)
            future_to_batch[future] = batch_size
        
        # 收集结果
        results = []
        for future in as_completed(future_to_batch):
            batch_size = future_to_batch[future]
            try:
                result = future.result()
                results.append(result)
                logging.info(f'成功构建引擎: {result}')
            except Exception as e:
                logging.error(f'构建引擎失败 (batch size {batch_size}): {e}')
    
    end_time = time.time()
    total_time = end_time - start_time
    
    logging.info(f'多进程构建完成，总耗时: {total_time:.2f}秒')
    logging.info(f'构建结果: {results}')
    
    return total_time, results

def test_sequential_build():
    """
    测试串行构建引擎（对比用）
    """
    batch_sizes = [50, 100, 200]
    
    logging.info(f'开始测试串行构建，batch sizes: {batch_sizes}')
    start_time = time.time()
    
    results = []
    for batch_size in batch_sizes:
        try:
            result = mock_build_engine(batch_size)
            results.append(result)
            logging.info(f'成功构建引擎: {result}')
        except Exception as e:
            logging.error(f'构建引擎失败 (batch size {batch_size}): {e}')
    
    end_time = time.time()
    total_time = end_time - start_time
    
    logging.info(f'串行构建完成，总耗时: {total_time:.2f}秒')
    logging.info(f'构建结果: {results}')
    
    return total_time, results

if __name__ == '__main__':
    logging.info('=' * 50)
    logging.info('开始测试多进程TensorRT引擎构建')
    logging.info('=' * 50)
    
    # 测试多进程构建
    logging.info('\n1. 测试多进程构建:')
    multiprocess_time, multiprocess_results = test_multiprocessing_build()
    
    # 测试串行构建
    logging.info('\n2. 测试串行构建:')
    sequential_time, sequential_results = test_sequential_build()
    
    # 性能对比
    logging.info('\n' + '=' * 50)
    logging.info('性能对比结果:')
    logging.info(f'串行构建时间: {sequential_time:.2f}秒')
    logging.info(f'多进程构建时间: {multiprocess_time:.2f}秒')
    
    if sequential_time > 0:
        speedup = sequential_time / multiprocess_time
        logging.info(f'性能提升: {speedup:.2f}x')
        logging.info(f'时间节省: {sequential_time - multiprocess_time:.2f}秒')
    
    logging.info('=' * 50)
