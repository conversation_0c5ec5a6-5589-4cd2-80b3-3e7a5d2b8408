import hashlib
import json
import os
import time
import logging
import shutil
from typing import Dict, Optional

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

import asyncio
import multiprocessing
from multiprocessing import Process, Pool, Manager
from components import cos, register_model, const, model_utils
from model_export import param_parser


def calc_sha1sum(filename: str):
    sha1 = hashlib.sha1()
    with open(filename, 'rb') as f:
        sha1.update(f.read())
    return sha1.hexdigest()


class ModelInstanceInfo(object):
    def __init__(self, model_name: str, model_version: str, model_conf_id: str, model_meta_path: str,
                 modify_time: str, cos_config: cos.COSConfig, creator: str):
        self.model_name = model_name
        self.model_version = model_version
        self.model_conf_id = model_conf_id
        self.model_meta_path = model_meta_path
        assert os.path.basename(model_meta_path) == 'model.meta'
        self.modify_time = time.mktime(time.strptime(modify_time, '%Y-%m-%d %H:%M:%S'))
        self.cos_config = cos_config
        self.creator = creator

    def get_model_parser(self, model_local_path: str):
        # download model meta file
        model_meta_local_path = os.path.join(model_local_path, 'model.meta')
        cos_client = cos.COSManager(self.cos_config)
        cos_client.init_client()
        cos_client.download_single_file(model_meta_local_path, self.model_meta_path)

        # init param parser
        parser = param_parser.NumerousDenseParameterParser(model_local_path)
        parser.parse_slice()
        return parser


class ModelInfo(object):
    def __init__(self, group_id: int, model_name: str):
        self.group_id = group_id
        self.model_name = model_name
        self.model_instances: Optional[Dict[str, ModelInstanceInfo]] = dict()

    # latest_version: 用户传入指定版本
    def list_model_instances(self, latest_version=None):
        http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                                 secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                                 config=Config())

        # latest_version 为空，接口返回最新版本
        if latest_version is None:
            latest_version = ''
        url = (f'{const.OPEN_API_HOST}{const.OpenApiUrl.MODEL_INSTANCE_INFO_GET.value}?'
               f'modelName={self.model_name}&modelVersion={latest_version}&ignoreClone=true')
        ret = http_client.get(url, header={})

        # output_model not registe of not instance
        # 输出模型没有注册或没有上传实例
        if ret['data'] is None:
            logging.info(
                'Query model %s instance fail with code %d: %s' % (self.model_name, ret['retCode'], ret['msg']))
            return

        model_instance_dict = ret['data']

        model_version = model_instance_dict['modelVersion']
        # 校验用户输入与venus反馈版本是否一致
        if latest_version != '' and latest_version != model_version:
            raise Exception(
                'The model_version Venus is returned as %s, But provided as %s.' % (model_version, latest_version))

        # 模型版本被删除
        if model_instance_dict['status'] == 1:
            raise Exception('The model %s version %s status is delete.' % (self.model_name, model_version))

        # 个人权限认证，需要命中以下条件：在模型所在的应用组、模型创建人、模型维护人、模型管理员; 非自管理桶，会返回 storageInfo
        '''
        if 'modelStorageInfo' not in model_instance_dict.keys():
            raise Exception('Unable to get %s model information, Please check your Model permissions...' % self.model_name)
        '''

        # 获取临时密钥
        cos_config, _ = register_model.get_single_storage_info(self.model_name, model_version)

        model_instance_info = ModelInstanceInfo(
            model_instance_dict['modelName'],
            model_version,
            model_instance_dict['modelId'],
            model_instance_dict['modelMetaPath'],
            model_instance_dict['modifyTime'],
            cos_config,
            model_instance_dict['creator']
        )
        self.model_instances[model_version] = model_instance_info

    @property
    def model_versions(self):
        versions = list(self.model_instances.keys())
        versions.sort(key=lambda version: [int(field) for field in version.split('_')])
        return versions


class ModelCache(object):
    def __init__(self, origin_model_name, max_entry=3):
        self.cache: Dict[str, param_parser.NumerousDenseParameterParser] = dict()
        self.max_entry = max_entry
        self.model_download = None

        # make temporary dir
        if const.TASK_ID is not None and const.TASK_ID != '':
            self.model_cache_dir = os.path.join('.', 'tasks', const.TASK_ID, origin_model_name)
        else:
            self.model_cache_dir = os.path.join('.', 'models', origin_model_name)
        os.makedirs(self.model_cache_dir, exist_ok=True)

    def add(self, version: str, model_instance: ModelInstanceInfo, refit_dependence: bool = False):
        model_path = os.path.join(self.model_cache_dir, version)
        os.makedirs(os.path.join(self.model_cache_dir, version), exist_ok=True)

        parser = model_instance.get_model_parser(model_path)
        self.cache[version] = parser
        # download dense part
        logging.info('Download dense part of model version %s...' % version)
        cos_root_dir = os.path.dirname(model_instance.model_meta_path)
        cos_manager = cos.COSManager(model_instance.cos_config)
        cos_manager.init_client()

        existed_graph_pb = False
        if const.GRAPH_PB_FILENAME in parser.model_slice['dense'].file_list:
            existed_graph_pb = True

        cos_download_filelist = list()
        for file in parser.model_slice['dense'].file_list:
            # 如果是refit依赖的版本，graph.pb 和 xxx.pbtxt 不下载，只需要下载 trt engine 和 reft.onnx 文件，或者evart 文件
            if refit_dependence:
                if not (file.endswith(const.TRT_ENGINE_FILENAME_SUFFIX)
                        or file.endswith(const.EVART_ENGINE_FILENAME_SUFFIX)
                        or file.endswith(const.ONNX_FILENAME_SUFFIX)):
                    continue
            # 优化模型下载时间：graph.pb 和 xxx.pbtxt 都存在时，只下载 graph.pb, 只存在一个时，下载存在的
            if file.endswith(const.GRAPH_PBTXT_FILENAME_SUFFIX) and existed_graph_pb:
                continue
            cos_download_filelist.append(file)
            # cos_manager.download_single_file(os.path.join(model_path, file), os.path.join(cos_root_dir, file))

        if len(cos_download_filelist) > 0:
            self.model_download = model_utils.ModelProcess(cos_root_dir, model_path, cos_manager, const.ACTION_TYPE_DOWNLOAD)
            result = self.model_download.run_multi_processes(cos_download_filelist)
            logging.info('model download result count: %d' % len(result))
            assert len(result) == len(cos_download_filelist), 'The number of download files is inconsistent!'

        # 只调用parse_dense_parameters_conf，为了获取模型依赖关系
        # read_dense_parameters在实际转换时手动调用，防止内存中加载太多版本的dense参数导致OOM
        # 对于refit依赖的版本，不需要下载和解析模型数据文件
        if not refit_dependence:
            parser.parse_dense_parameters_conf()

    def __getitem__(self, key: str):
        return self.cache[key]

    def get_all_versions(self):
        return list(self.cache.keys())

    def clean(self, skip_verion=None):
        # only clean versions that have been converted
        versions = [version for version in self.get_all_versions() if self.cache[version].param_parsed]
        versions.sort(key=lambda version: [int(field) for field in version.split('_')])
        if len(versions) > self.max_entry:
            for version in versions[:-self.max_entry]:
                if version == skip_verion:
                    logging.info('Skip clean model version %s in cache' % version)
                    continue
                logging.info('Clean model version %s in cache...' % version)
                self.remove(version)

    def remove(self, version: str):
        del self.cache[version]
        shutil.rmtree(os.path.join(self.model_cache_dir, version), ignore_errors=True)


class ModelCopy():

    def __init__(self, cos_config, original_cos_path, output_cos_path, copy_manager):

        self.src_dir = original_cos_path
        self.dest_dir = output_cos_path
        self.copy_config = cos_config
        self.copy_manager = copy_manager

    async def cos_copy(self, file_name):

        src_dir = os.path.join(self.src_dir, file_name)
        dest_dir = os.path.join(self.dest_dir, file_name)
        self.copy_manager.copy_file(src_dir, dest_dir)

        return file_name

    def split_task_lists(self, iterable, chunks=1):
        split_lists = list(iterable)
        return [split_lists[i::chunks] for i in range(chunks)]

    def run_subprocesses(self, lists, queue):
        """
        子进程：接收任务列表和用于进程间通讯的Queue
        """
        loop = asyncio.new_event_loop()
        tasks = [loop.create_task(self.cos_copy(file_name)) for file_name in lists]
        # 协程
        loop.run_until_complete(asyncio.wait(tasks))
        for task in tasks:
            queue.put(task.result())

    def run_multi_processes(self, task_lists):
        """
        父进程：分割任务并初始化进程池，启动进程并返回结果
        """
        process_count = const.MAX_COROUTINE_NUM  # 90
        if len(task_lists) < process_count:
            process_count = len(task_lists)  # 文件数
        print('process_count: %d' % process_count)

        split_lists = self.split_task_lists(task_lists, process_count)
        pool = multiprocessing.Pool(process_count)
        queue = Manager().Queue()
        for lists in split_lists:
            pool.apply_async(self.run_subprocesses, args=(lists, queue,))
        # 调用join之前，先调用close
        # 执行完close后不会有新的进程加入到pool，join函数等待所有子进程结束
        pool.close()
        pool.join()
        result = []
        # 从子进程读取结果并返回
        while not queue.empty():
            result.append(queue.get())
        return result


class ModelProcess:
    def __init__(self, src_path, dst_path, cos_manager, action):
        self.src_dir = src_path
        self.dest_dir = dst_path
        self.cos_manager = cos_manager
        self.action = action

    async def cos_copy(self, file_name):
        src_dir = os.path.join(self.src_dir, file_name)
        dest_dir = os.path.join(self.dest_dir, file_name)
        self.cos_manager.copy_file(src_dir, dest_dir)

        return file_name

    async def cos_upload(self, file_name):
        src_filename = os.path.join(self.src_dir, file_name)
        dest_filename = os.path.join(self.dest_dir, file_name)
        self.cos_manager.upload_single_file(src_filename, dest_filename)

        return file_name

    async def cos_download(self, file_name):
        src_filename = os.path.join(self.src_dir, file_name)
        dest_filename = os.path.join(self.dest_dir, file_name)
        self.cos_manager.download_single_file(dest_filename, src_filename)

        return file_name

    def split_task_lists(self, iterable, chunks=1):
        split_lists = list(iterable)
        return [split_lists[i::chunks] for i in range(chunks)]

    def run_subprocesses(self, lists, queue):
        """
        子进程：接收任务列表和用于进程间通讯的Queue
        """
        loop = asyncio.new_event_loop()
        if self.action == const.ACTION_TYPE_COPY:
            tasks = [loop.create_task(self.cos_copy(file_name)) for file_name in lists]
        elif self.action == const.ACTION_TYPE_UPLOAD:
            tasks = [loop.create_task(self.cos_upload(file_name)) for file_name in lists]
        elif self.action == const.ACTION_TYPE_DOWNLOAD:
            tasks = [loop.create_task(self.cos_download(file_name)) for file_name in lists]
        else:
            assert False, 'Unknown action type!'

        # 协程
        loop.run_until_complete(asyncio.wait(tasks))
        for task in tasks:
            queue.put(task.result())

    def run_multi_processes(self, task_lists):
        """
        父进程：分割任务并初始化进程池，启动进程并返回结果
        """
        process_count = const.MAX_COROUTINE_NUM  # 90
        if len(task_lists) < process_count:
            process_count = len(task_lists)  # 文件数
        print('process_count: %d' % process_count)

        split_lists = self.split_task_lists(task_lists, process_count)
        pool = multiprocessing.Pool(process_count)
        queue = Manager().Queue()
        for lists in split_lists:
            pool.apply_async(self.run_subprocesses, args=(lists, queue,))
        # 调用join之前，先调用close
        # 执行完close后不会有新的进程加入到pool，join函数等待所有子进程结束
        pool.close()
        pool.join()
        result = []
        # 从子进程读取结果并返回
        while not queue.empty():
            result.append(queue.get())
        return result
