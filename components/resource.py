import logging
from typing import Optional, List

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

from components import const
from components.const import OpenApiUrl, OpenApiHost


def query_resource_by_vrn(vrn) -> dict:
    """
    通过vrn查询资源详情
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    url = f'{OpenApiHost.FORMAL.value}{OpenApiUrl.RESOURCE_GET_BY_VRN.value}?vrn={vrn}'

    logging.info('query resource: %s' % url)
    try:
        ret = http_client.get(url, header={})
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return {}
    logging.info(f'query resource, ret:{ret}')
    if ret['code'] != 0:
        return {}
    return ret['data']


def query_gpu_type_by_resource_group_name(resource_group) -> str:
    """
    通过GPU资源组名称查询gpu卡类型
    """
    vrn = f'vrn://@global/gpu/{resource_group}'
    data = query_resource_by_vrn(vrn)
    gpu_res_info = GpuResInfo(data)
    default_gpu = "unknown"
    # 如果没有绑定resTag信息，则使用 default_gpu 值返回
    if gpu_res_info.resTags is None or len(gpu_res_info.resTags) <= 0:
        return default_gpu
    res_tags = gpu_res_info.resTags
    filtered_list = list(filter(lambda tag: tag.tagType == 'gpu_type', res_tags))

    # 绑定的信息不合法
    if len(filtered_list) <= 0:
        return default_gpu
    else:
        return filtered_list[0].tagValue


class ResTagInfo:
    """
    Res tag配置信息
    """

    def __init__(self, dictionary):
        for key, value in dictionary.items():
            setattr(self, key, value)

    id: int

    tagCode: str

    tagValue: str

    tagLabel: str

    tagType: str

    tagRes: str


class GpuResInfo:
    """
    Gpu 存储资源配置
    """

    def __init__(self, dictionary):
        for key, value in dictionary.items():
            if key == 'resTags':
                res_tags = []
                for tag in value:
                    res_tags.append(ResTagInfo(tag))
                setattr(self, key, res_tags)
            else:
                setattr(self, key, value)

    resId: int

    poolName: str

    boreClusterId: str

    resTag: str

    poolNameNew: str

    resTags: Optional[List[ResTagInfo]] = []
