import json
import logging
from typing import List

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

from components import const, cos
from components.const import OpenApiUrl
from utils.with_retry_wrapper import with_retry


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def __query_model_conf(model_name: str):
    logging.info('Query model conf: %s' % model_name)

    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())

    url = f'{const.OPEN_API_HOST}{OpenApiUrl.MODEL_INFO_GET.value}?modelName={model_name}'
    ret = http_client.get(url, header=dict())
    if ret['retCode'] != 0:
        logging.info('Query model info fail with code %d: %s' % (ret['retCode'], ret['msg']))
        return None
    return ret['data']


def __has_key_config_changed(origin_model: dict, output_model: dict, key_config_name_list: List[str]) -> bool:
    """
    判断原模型关键配置是否发生改变
    """
    for config_name in key_config_name_list:
        if origin_model.get(config_name) != output_model.get(config_name):
            return True
    return False


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def __save_model(model_info: dict):
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    header = {'Content-Type': 'application/json'}
    body = json.dumps(model_info)
    logging.info('Save model request body: %s' % body)
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_SAVE.value
    ret = http_client.post(url, header=header, body=body)
    if ret['retCode'] != 0:
        raise Exception('Save model fail with code %d: %s' % (ret['retCode'], ret['msg']))
    model_conf_id = ret['data']['id']
    logging.info('Save model succeed, model_conf_id: %d' % model_conf_id)
    return model_conf_id


def check_and_save_model_info(group_id: int, original_model_name: str, output_model_name: str,
                              keep_version: int, job_id: int) -> int:
    """
    导出模型没注册或原模型配置发生改变，则注册或更新模型信息，并返回模型配置id
    """
    logging.info('Start to check and save model %s' % output_model_name)
    origin_model = __query_model_conf(original_model_name)
    if origin_model is None:
        raise Exception('Query origin model failed')
    output_model = __query_model_conf(output_model_name)
    # 导出模型未注册，或者导出模型已注册，但是原模型关键配置发生改变，才需要进行后续操作，注册或更新导出模型
    key_config_name_list = ['modelParm', 'enableFeaturePlatform', 'sceneSampleId', 'trainSampleId', 'sampleVersion',
                            'sceneId']
    if output_model is not None and not __has_key_config_changed(origin_model, output_model, key_config_name_list):
        logging.info('Model %s already exist in group %d, model_conf_id: %d, skip' %
                     (output_model_name, group_id, output_model['id']))
        return output_model['id']

    maintainer = const.OPERATOR
    if maintainer == '':
        maintainer = origin_model.get('creator')
    output_model_info = {
        'modelName': output_model_name,
        'modelTrainFrame': 'numerous',
        'modelTrainAlg': 'rtml',
        'modelTrainType': 'online',
        'description': '无量模型TensorRT转换模型',
        'appGroupId': group_id,
        'jobId': job_id,
        'keepVersions': keep_version,
        'maintainer': maintainer
    }
    for config_name in key_config_name_list:
        output_model_info[config_name] = origin_model.get(config_name)
    return __save_model(output_model_info)


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def register_model_instance_storage(model_name: str, model_version: str, model_conf_id: int,
                                    model_meta_path: str, creator: str, cos_config: dict):
    # 对fun(register_model_instance) 升级，实现模型实例注册功能
    # register model instance
    logging.info('Start to register model instance %s, version: %s, creator: %s, modelPath: %s' % \
                 (model_name, model_version, creator, model_meta_path))
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())

    body_dict = {
        'modelName': model_name,
        'modelId': model_conf_id,
        'modelMetaPath': model_meta_path,
        'maintainer': creator,
        'modelVersion': model_version,
        'modelStorageInfo': json.dumps(cos_config)
    }

    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info('Request body: %s' % body)
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_INSTANCE_REGISTER.value
    ret = http_client.post(url, header=header, body=body)
    if ret['retCode'] != 0:
        raise Exception('Register model instance fail with code %d: %s' % (ret['retCode'], ret['msg']))

    logging.info('Register model instance succeed')


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def get_single_storage_info(model_name: str, model_version: str):
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())

    url = (f'{const.OPEN_API_HOST}{OpenApiUrl.MODEL_TEMP_STORAGE_INFO.value}?'
           f'modelName={model_name}&modelVersion={model_version}')
    ret = http_client.get(url, header={})

    if ret['retCode'] != 0:
        raise Exception('Query model instance fail with code %d: %s' % (ret['retCode'], ret['msg']))

    model_storage_info_dict = ret['data']
    owner = model_storage_info_dict['owner']
    if owner == "business":
        token = None
    else:  # owner = "venus"
        token = model_storage_info_dict['token']

    cos_config = cos.COSConfig(
        model_storage_info_dict['bucketName'],
        model_storage_info_dict['accessKey'],
        model_storage_info_dict['secretKey'],
        model_storage_info_dict['region'],
        model_storage_info_dict['sid'],
        model_storage_info_dict['endpoint'],
        token
    )
    return cos_config, owner


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def get_copy_storage_info(src_model_name: str, src_model_version: str,
                          dest_model_name: str, dest_model_version: str):
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())

    url = (f'{const.OPEN_API_HOST}{OpenApiUrl.MODEL_COPY_TEMP_STORAGE_INFO.value}?destModelName={dest_model_name}'
           f'&destModelVersion={dest_model_version}&srcModelName={src_model_name}&srcModelVersion={src_model_version}')
    ret = http_client.get(url, header={})

    if ret['retCode'] != 0:
        raise Exception('Query model instance fail with code %d: %s' % (ret['retCode'], ret['msg']))

    # 模型实例注册结构体信息
    model_storage_info_dict = ret['data']['destTempStorageInfo']

    owner = model_storage_info_dict['owner']
    if owner == "business":
        token = None
    else:  # owner = "venus"
        token = model_storage_info_dict['token']

    cos_config = cos.COSConfig(
        model_storage_info_dict['bucketName'],
        model_storage_info_dict['accessKey'],
        model_storage_info_dict['secretKey'],
        model_storage_info_dict['region'],
        model_storage_info_dict['sid'],
        model_storage_info_dict['endpoint'],
        token
    )

    return cos_config, model_storage_info_dict


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def parse_model_incremental_status(model_name: str, latest_version: str):
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())

    if latest_version is None:
        latest_version = ''
    url = (f'{const.OPEN_API_HOST}{OpenApiUrl.MODEL_INSTANCE_INFO_GET.value}?'
           f'modelName={model_name}&modelVersion={latest_version}')
    ret = http_client.get(url, header={})

    model_instance_dict = ret['data']
    if 'depModelVersion' in model_instance_dict.keys():
        const.INCREMENTAL = True
        logging.info('Original model %s is incremental model.' % model_name)
