from enum import Enum

VENUS_OP_CODE = None
VENUS_OP_VERSION = None

OPERATOR = 'ttsybyweng'

VENUS_OPENAPI_SECRET_ID = None
VENUS_OPENAPI_SECRET_KEY = None
USER_OPENAPI_SECRET_ID = None
USER_OPENAPI_SECRET_KEY = None

INCREMENTAL = False

RETRY_COUNT = 3
RETRY_INTERVAL = 5.0
MAX_RETRY_INTERVAL = 60.0

MAX_COROUTINE_NUM = 90

GRAPH_AFTER_CLEAN_FILENAME = 'graph_after_clean.pb'
GRAPH_AFTER_FUSE_FILENAME = 'graph_after_fuse.pb'
GRAPH_CPU_INFER_FILENAME = 'graph_cpu_infer.pb'
FROZEN_GRAPH_FILENAME = 'frozen_graph.pb'

WEIGHT_TRANSPOSE_FILENAME = 'weight_transpose.txt'

GRAPH_PB_FILENAME = 'graph.pb'
GRAPH_PBTXT_FILENAME_SUFFIX = '.pbtxt'
TRT_ENGINE_FILENAME_SUFFIX = '.engine'
EVART_ENGINE_FILENAME_SUFFIX = '.evart'
ONNX_FILENAME_SUFFIX = 'refit.onnx'
VARIABLES_FILENAME_PREFIX = 'variables'

ACTION_TYPE_COPY = 'copy'
ACTION_TYPE_UPLOAD = 'upload'
ACTION_TYPE_DOWNLOAD = 'download'

PYTHON_RUN = '/data/miniconda3/envs/env-3.8.8/bin/python3'

USE_REFIT = True
ENGINE_PRECISION = "FP32"

ENGINE_VERSION = ""

ORI_MODEL_NAME = None

TRT_CONFIG_FILE_NAME = 'trt_config.json'
EVART_CONFIG_FILE_NAME = 'evart_config.json'
EVA_RESULT_FILE_NAME = 'eva_result.json'

# trt是否使用evart工具生成engine:画布默认不使用evart工具生成engine
TRT_GENERATE_ENGINE_BY_EVART_TOOL = False
EVART_CONVERTER_SCRIPT = './evart/evartConverterTool'

CONVERT_TYPE = None

OPEN_API_HOST = None

# 是否允许重复转模
ALLOW_REPEAT_CONVERT = False

# tensor rt plugin so 的 sha1sum 值，更新 so 后需要同时更新
TRT_LIBNVINFER_PLUGIN_SHA1SUM = '1a1791e825d7f705e29b981663b8043dd42cd3c4'

OPSET_VERSION_DEFAULT = 9

# TRT版本
TRT_VERSION = ""
GPU_DEVICE_TYPE = ""
CUDA_VERSION = ""

# 当前任务的ID
TASK_ID = None


class ConvertType(Enum):
    """
    转换类型：任务上报粘合层区分trt/evart转模
    """
    NUMEROUS_TRT = 'Numerous2Trt'
    NUMEROUS_EVART = 'Numerous2Evart'


class SubTaskType(Enum):
    """
    转换子任务类型
    """
    READ_DENSE = 'ReadDense'
    GENERATE_ENGINE = 'GenerateEngine'
    REGISTER_MODEL = 'RegisterModel'


class OpenApiHost(Enum):
    """
    不同环境openapi请求地址
    """
    FORMAL = 'http://v2.open.venus.oa.com'
    PRE = 'http://pre.v2.open.venus.oa.com'
    TEST = 'http://test.v2.open.venus.oa.com'


class OpenApiUrl(Enum):
    """
    openapi接口地址
    """
    TASK_DURATION_SAVE = '/task/duration/save'
    MODEL_TRANSFORM_GET = '/model/tansform/get'
    MODEL_TRANSFORM_INSERT = '/model/tansform/insert'
    MODEL_TRANSFORM_UPDATE = '/model/tansform/update'
    RESOURCE_GET_BY_VRN = '/resource/get/by/vrn'
    MODEL_INFO_GET = '/model/info/get'
    MODEL_INSTANCE_INFO_GET = '/model/instance/info/get'
    MODEL_SAVE = '/model/save?ModelRegisterDto='
    MODEL_INSTANCE_REGISTER = '/model/storage/instance/register?ModelInstanceRegisterDto='
    WORKFLOW_JOB_INSTANCE_GET = '/workflow/job/instance/get'
    MODEL_TEMP_STORAGE_INFO = '/model/temp/storage/info'
    MODEL_COPY_TEMP_STORAGE_INFO = '/model/copy/temp/storage/info'
    MODEL_CONVERT_RECORD_UPDATE = '/model/convert/record/update'


class Status(Enum):
    """
    转模任务状态
    """
    RUNNING = 1
    SUCCESS = 2
    FAILED = 3


# ONNX算子白名单
ONNX_OPERATOR_WHITE_LIST = ["Log1p", "BroadcastGradientArgs", "ReluGrad"]
