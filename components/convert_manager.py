import json
import logging

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

from components import const
from components.const import OpenApiUrl
from utils.with_retry_wrapper import with_retry


def update_convert_manager_progress(output_model_name: str, output_model_version: str, task_count: int,
                                    current_task_num: int, current_model_version: str):
    """
    更新convert_manager任务进展
    """
    body_dict = {
        'output_model_name': output_model_name,
        'output_model_version': output_model_version,
        'task_count': task_count,
        'current_task_num': current_task_num,
        'current_model_version': current_model_version,
    }
    try:
        update_convert_manager_record(body_dict)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return


def update_convert_manager_err_msg(output_model_name: str, output_model_version: str, err_msg: str):
    """
    更新convert_manager任务失败信息
    """
    body_dict = {
        'output_model_name': output_model_name,
        'output_model_version': output_model_version,
        'err_msg': err_msg,
    }
    try:
        update_convert_manager_record(body_dict)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return


@with_retry(const.RETRY_COUNT, const.RETRY_INTERVAL)
def update_convert_manager_record(body_dict: dict):
    """
    更新convert_manager任务记录
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info(f'update convert manager record, body:{body}')
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_CONVERT_RECORD_UPDATE.value
    http_client.post(url, header=header, body=body)
