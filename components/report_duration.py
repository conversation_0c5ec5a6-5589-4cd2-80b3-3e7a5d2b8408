import json
import logging
import time

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

from components import const
from components.const import OpenApiUrl


def report_duration(task_id: int, task_type: str, start_time: float, finish_time: float) -> None:
    """
    指定任务ID、任务类型，更新任务耗时，如果有父任务则做为子任务插入或更新耗时，如果没有父任务则作为父更新插入或更新耗时
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    body_dict = {
        'parentTaskType': const.CONVERT_TYPE,
        'taskType': task_type,
        'taskId': task_id,
        'startTime': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
        'finishTime': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(finish_time)),
    }
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info('report duration request body: %s' % body)
    url = const.OPEN_API_HOST + OpenApiUrl.TASK_DURATION_SAVE.value
    try:
        ret = http_client.post(url, header=header, body=body)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return
    logging.info(f'report duration, ret:{ret}')
