import json
import logging
from typing import List, Optional

from venus_api_base.config import Config
from venus_api_base.http_client import HttpClient

from components import const
from components.const import Status, OpenApiUrl
from components.resource import query_gpu_type_by_resource_group_name

# 画布作业运行状态
JOB_STATUS_FINISH = 30 # 运行结束

class TaskRecord:
    """
    任务纪录
    """

    def __init__(self, dictionary):
        for key, value in dictionary.items():
            setattr(self, key, value)

    # 任务Id
    id: int
    # 原模型名
    originalModelName: str
    # 原模型版本
    originalModelVersion: str
    # 输出模型名
    outputModelName: str
    # 输出模型版本
    outputModelVersion: str
    # 转换类型
    type: str
    # 转换参数
    param: str
    # 作业ID
    jobId: int
    # 会话ID
    sessionId: str
    # 运行时数据
    runtimeData: str
    # 转换状态:0-待执行、1-执行中、2-执行成功、3-执行失败
    status: str
    # 开始时间
    startTime: str


def get_latest_task_record(output_model_name: str, output_model_version: str) -> Optional[TaskRecord]:
    """
    请求粘合层查询转模任务记录
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    body_dict = {
        'outputModelName': output_model_name,
        'outputModelVersion': output_model_version,
    }
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info(f'Get task record, body:{body}')
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_TRANSFORM_GET.value
    latest_task_record: Optional[TaskRecord] = None
    try:
        ret = http_client.post(url, header=header, body=body)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return latest_task_record
    logging.info(f'Get task record, ret:{ret}')
    if ret['code'] != 0:
        return latest_task_record
    for task_info in ret['data']:
        task_record = TaskRecord(task_info)
        if latest_task_record is None or latest_task_record.startTime < task_record.startTime:
            latest_task_record = task_record
    return latest_task_record


def create_task_record(original_model_name: str, output_model_name: str, version: str, group_id: int, job_id: int,
                       session_id: str, predict_target: List[str], keep_version: int, batch_size_list: List[int],
                       placeholder_replace: List[int], engine_type: str, fp16: bool, params_pairs: str,
                       resource_group_name: str, opset_version: int) -> int:
    """
    请求粘合层记录转模任务记录，用于记录每次转模情况，
    不会根据请求参数（原始模型、原始版本、输出应用组、卡型、predict_target、batch_size等）查询并替换已完成或未完成任务
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    param = {
        'predictTarget': predict_target,
        'keepVersion': keep_version,
        'batchSizeList': batch_size_list,
        'placeholderReplace': placeholder_replace,
        'engineType': engine_type,
        'engineVersion': const.ENGINE_VERSION,
        'fp16': fp16,
        'paramsPairs': params_pairs,
        'opCode': const.VENUS_OP_CODE,
        'opVersion': const.VENUS_OP_VERSION,
        'opsetVersion': opset_version,
        'trtVersion': const.TRT_VERSION,
        'resourceGroupName': resource_group_name,
        'gpuType': query_gpu_type_by_resource_group_name(resource_group_name),
    }
    body_dict = {
        'originalModelName': original_model_name,
        'originalModelVersion': version,
        'outputModelName': output_model_name,
        'outputModelVersion': version,
        'outputGroupId': group_id,
        'type': const.CONVERT_TYPE,
        'param': json.dumps(param),
        'status': Status.RUNNING.value,
        'jobId': job_id,
        'sessionId': session_id,
    }
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info(f'create task record, body:{body}')
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_TRANSFORM_INSERT.value
    try:
        ret = http_client.post(url, header=header, body=body)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return -1
    logging.info(f'create task record, ret:{ret}')
    if ret['retCode'] != 0:
        return -1
    task_id = ret['data']
    return task_id


def update_task_record(task_id: int, runtime_data: str, status: int) -> None:
    """
    指定任务ID，更新运行时数据和任务状态
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    body_dict = {
        'id': task_id,
        'runtimeData': runtime_data,
        'status': status
    }
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)
    logging.info(f'update task record, body:{body}')
    url = const.OPEN_API_HOST + OpenApiUrl.MODEL_TRANSFORM_UPDATE.value
    try:
        ret = http_client.post(url, header=header, body=body)
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return
    logging.info(f'update task record, ret:{ret}')


def is_task_running(job_id:int, session_id:str) -> bool:
    """
    通过组件运行状态判断历史转模任务是否在运行中
    不能仅通过任务纪录判断是否为运行中，因为任务纪录状态为运行中，可能是组件异常中断还未变更状态，实际组件任务已结束
    """
    http_client = HttpClient(secret_id=const.VENUS_OPENAPI_SECRET_ID,
                             secret_key=const.VENUS_OPENAPI_SECRET_KEY,
                             config=Config())
    url = f'{const.OPEN_API_HOST}{OpenApiUrl.WORKFLOW_JOB_INSTANCE_GET.value}?jobId={job_id}&session={session_id}'
    try:
        ret = http_client.get(url, header={})
    except Exception as e:  # pylint: disable=broad-except
        logging.error(f'Exception:{e}')
        return False
    logging.info(f'Get workflow job instance, ret:{ret}')
    if ret['code'] != 0:
        return False
    if ret['data']['status'] != JOB_STATUS_FINISH:
        return True
    return False


def is_repeat_convert(output_model_name: str, all_versions: List[str]) -> bool:
    """
    判断是否重复转模，避免重复转模导致转模失败
    """
    if const.ALLOW_REPEAT_CONVERT:
        return False
    for version in all_versions:
        task_record = get_latest_task_record(output_model_name, version)
        # 没有转换过则本次可转
        if task_record is None:
            continue
        # 之前转换失败，本次也可以继续转
        if task_record.status == Status.FAILED.value:
            continue
        # 之前已经转换成功过，则本次不用再转
        task_url = f'https://venus.woa.com/bore.html#/jobinstance/{task_record.jobId}/{task_record.sessionId}'
        if task_record.status == Status.SUCCESS.value:
            logging.info(f'The conversion was successful before: {task_url}')
            return True
        # 若已存在运行中的任务，则本次不用再转
        if is_task_running(task_record.jobId, task_record.sessionId):
            logging.info(f'There is already a running task: {task_url}')
            return True
    return False
