#!/bin/bash

if [[ "${ENV_TASK_ID}" == "" || "${ENV_MODEL_NAME}" == "" ]]; then
    echo "ENV_MODEL_NAME is empty, exit"
    exit 1
fi

rm -f eva_result.json
mkdir -p ./tasks/"${ENV_TASK_ID}"
export LC_ALL=en_US.utf-8
export LANG=en_US.utf-8
. /data/miniconda3/etc/profile.d/conda.sh
conda activate env-3.8.8
python3 worker_main.py
exit_code=$?

if [[ "${KEEP_MODEL_FILE}" == "true" ]]; then
    echo "KEEP_MODEL_FILE is true, skip remove tasks/${ENV_TASK_ID}"
else
    model_path="./tasks/${ENV_TASK_ID}"
    if [[ -d ${model_path} ]]; then
        echo "remove ${model_path}"
        sleep 5
        rm -fr "${model_path}"
    fi
fi

exit $exit_code
