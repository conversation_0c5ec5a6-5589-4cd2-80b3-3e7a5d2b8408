# TensorRT引擎构建多进程优化

## 概述

本次改进将TensorRT引擎构建过程从串行处理改为多进程并行处理，显著提高了多个batch size引擎的构建效率。

## 主要改进

### 1. 新增多进程构建方法

#### `build_trt_engine_multiprocess()`
- **功能**: 并行构建多个batch size的TensorRT引擎
- **优势**: 多个引擎可以同时构建，减少总构建时间
- **进程数**: 根据batch size数量和系统配置自动调整

#### `convert_engine_for_refit()`
- **功能**: refit模式下的串行构建（保持原有逻辑）
- **适用场景**: 使用refit技术加速引擎构建时，但保持串行处理
- **说明**: 该方法保持原有的串行处理逻辑，不使用多进程

### 2. 辅助方法

#### `_build_single_trt_engine()`
- **功能**: 单个进程构建引擎的辅助方法
- **运行环境**: 在子进程中执行

## 技术实现

### 进程池管理
- 使用Python `concurrent.futures.ProcessPoolExecutor` 管理进程池
- 进程数量根据任务数量和系统配置自动调整
- 最大进程数限制在 `const.MAX_COROUTINE_NUM` (90)

### ProcessPoolExecutor的优势
- **更现代的API**: 提供更简洁和直观的接口
- **Future对象**: 支持异步操作和结果回调
- **异常处理**: 更好的异常传播和处理机制
- **资源管理**: 自动的进程池生命周期管理
- **兼容性**: 与标准库的ThreadPoolExecutor保持一致的API

### 任务分发
- 为每个batch size创建独立的任务
- 第一个任务标记为 `is_first=True`，用于推理结果验证
- 使用 `executor.submit()` 提交任务，返回Future对象

### 结果收集
- 使用 `as_completed()` 按完成顺序收集结果
- 完善的异常处理和错误日志记录
- 返回构建成功的引擎文件列表

## 性能提升

### 适用范围
多进程优化仅适用于标准TensorRT引擎构建（`convert_engine()`方法），refit相关操作保持原有的串行处理逻辑。

### 串行处理 (原版本)
```
Batch 50: 构建时间 T1
Batch 100: 构建时间 T2  
Batch 200: 构建时间 T3
总时间 = T1 + T2 + T3
```

### 多进程处理 (新版本)
```
Batch 50, 100, 200: 并行构建
总时间 ≈ max(T1, T2, T3)
```

### 预期提升
- **2个batch size**: 提升约 1.5-2倍
- **3个batch size**: 提升约 2-3倍
- **更多batch size**: 提升效果更明显

### 注意事项
- refit构建和更新操作保持串行处理，确保操作稳定性
- 多进程优化主要针对标准引擎构建场景

## 使用方法

### 自动启用
多进程构建会自动启用，无需额外配置。当检测到多个batch size时，系统会自动选择多进程模式。

### 多进程使用范围
- **标准构建**: `convert_engine()` 方法使用多进程并行构建
- **Refit构建**: `convert_engine_for_refit()` 方法保持串行处理（保持原有逻辑）
- **Refit更新**: `convert_engine_use_refit()` 方法保持串行处理（保持原有逻辑）

### 配置参数
- `const.MAX_COROUTINE_NUM`: 最大并发进程数 (默认90)
- `self.batch_size_list`: batch size列表

### 核心API
```python
# 使用ProcessPoolExecutor创建进程池
with ProcessPoolExecutor(max_workers=process_count) as executor:
    # 提交任务
    future = executor.submit(self._build_single_trt_engine, *args)
    
    # 收集结果
    for future in as_completed(future_to_batch):
        result = future.result()
```

## 兼容性

### 向后兼容
- 保持原有的API接口不变
- 单batch size场景仍使用原有逻辑
- 不影响其他功能的正常运行

### 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 失败任务的错误信息反馈

## 注意事项

### 资源使用
- 多进程会增加内存和CPU使用
- 建议在资源充足的机器上使用
- 进程数会根据系统资源自动调整

### 设计决策说明
- **`convert_engine_for_refit()`保持串行**: 该方法涉及复杂的refit逻辑和文件依赖关系，保持串行处理可以确保操作的稳定性和可预测性
- **`convert_engine_use_refit()`保持串行**: 该方法涉及基于现有引擎的refit更新，保持串行处理可以确保文件依赖关系的正确性
- **`convert_engine()`使用多进程**: 标准构建操作相对独立，适合并行处理
- **向后兼容**: 保持原有refit相关逻辑不变，确保现有工作流程的稳定性

### 依赖要求
- 需要Python multiprocessing模块
- 确保TensorRT环境在多进程下正常工作
- GPU资源在多进程间合理分配

## 测试建议

### 功能测试
1. 测试不同batch size数量的场景
2. 验证refit模式下的多进程构建
3. 检查异常情况下的错误处理

### 性能测试
1. 对比串行和多进程的构建时间
2. 测试不同进程数下的性能表现
3. 监控系统资源使用情况

## 未来优化方向

1. **动态进程数调整**: 根据系统负载动态调整进程数
2. **GPU资源管理**: 更好的GPU资源分配策略
3. **任务优先级**: 支持任务优先级调度
4. **监控指标**: 添加更详细的性能监控指标
