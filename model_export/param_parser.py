import gzip
import logging
import os
import re
import struct
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Binary<PERSON>, Dict, List, Optional, Set

import json
import numpy as np
from lxml import etree
from components.const import OPSET_VERSION_DEFAULT, ENGINE_PRECISION


class SliceDef(object):
    def __init__(self, slice_name: str, slice_type: str):
        self.slice_name = slice_name
        self.slice_type = slice_type
        self.file_list: Set[str] = set()

        self.graph_file: Optional[str] = None
        self.binary_graph_file: Optional[str] = None
        self.conf_file: str = ''
        self.meta_files: Dict[str, str] = dict()
        self.data_files: Dict[str, str] = dict()

        self.trt_engine_files: Dict[str, str] = dict()
        self.onnx_files: Dict[str, str] = dict()

    def add_file(self, file_name: str):
        self.file_list.add(file_name)

    def parse_files(self):
        for filename in self.file_list:
            basename, ext = os.path.splitext(filename)
            if ext == '.pbtxt':
                self.graph_file = filename
            elif filename == 'graph.pb':
                self.binary_graph_file = filename
            elif ext == '.conf':
                self.conf_file = filename
            elif ext == '.meta':
                self.meta_files[basename] = filename
            elif ext == '.data':
                self.data_files[basename] = filename
            elif ext == '.engine':
                self.trt_engine_files[basename] = filename
            elif ext == '.onnx':
                self.onnx_files[basename] = filename


class ParameterPartition(object):
    def __init__(self, row_begin: int, row_end: int, col_begin: int, col_end: int):
        self.row_begin = row_begin
        self.row_end = row_end
        self.col_begin = col_begin
        self.col_end = col_end


class ParameterDef(object):
    def __init__(self, model_slice: str, param_id: str, param_name: str, param_row_num: str,
                 param_col_num: str, shape: Optional[List[int]], placeholder_name: str, update_op_name: str):
        self.model_slice = model_slice
        self.param_id = param_id
        self.param_name = param_name
        self.param_row_num = int(param_row_num)
        self.param_col_num = int(param_col_num)
        self.shape = shape
        self.placeholder_name = placeholder_name
        self.update_op_name = update_op_name

        self._param = np.zeros((self.param_row_num, self.param_col_num), dtype=np.float32)
        self._param_mask = np.zeros((self.param_row_num, self.param_col_num), dtype=np.bool)

    def set_parameter(self, partition: ParameterPartition, val: np.ndarray):
        self._param[partition.row_begin: partition.row_end + 1, partition.col_begin: partition.col_end + 1] = val
        self._param_mask[partition.row_begin: partition.row_end + 1, partition.col_begin: partition.col_end + 1] = True

    def get_parameter(self):
        if self.shape is None:
            return np.array(self._param.item())
        else:
            return np.reshape(self._param, self.shape)

    def check_parameter_all_set(self):
        return np.all(self._param_mask)


class ControlPlaceholderDef(object):
    def __init__(self, cph_name: str, cph_type: str, cph_value: str):
        self.cph_name = cph_name
        self.cph_type = cph_type
        if cph_type == 'BOOL':
            self.cph_value = np.array([int(cph_value)], dtype=np.bool)
        elif cph_type == 'INT':
            self.cph_value = np.array([int(cph_value)], dtype=np.int32)
        elif cph_type == 'FLOAT':
            self.cph_value = np.array([float(cph_value)], dtype=np.float32)
        elif cph_type == 'STRING':
            self.cph_value = np.array([cph_value], dtype=np.object)
        else:
            raise Exception('Unknown control placeholder data type %s' % cph_type)


class DensePlaceholderDef(object):
    def __init__(self, dph_name: str, size: int):
        self.dph_name = dph_name
        self.size = size


class SparsePlaceholderDef(object):
    def __init__(self, table_name: str, is_sparse: bool, ph_name: str):
        self.table_name = table_name
        self.is_sparse = is_sparse
        self.ph_name = ph_name


class NumerousDenseParameterParser(object):
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.conf_parsed: bool = False
        self.param_parsed: bool = False

        self.model_version: str = ''
        self.model_format: str = ''
        self.model_format_version: str = ''
        self.deps: List[str] = list()

        self.model_slice: Dict[str, SliceDef] = dict()

        self.parameters: Dict[str, Dict[str, ParameterDef]] = dict()
        self.control_placeholders: Dict[str, Dict[str, ControlPlaceholderDef]] = dict()
        self.predict_target: Optional[List[str]] = None
        self.dense_placeholders: Dict[str, DensePlaceholderDef] = dict()
        self.sparse_placeholders: Dict[str, SparsePlaceholderDef] = dict()

        self.phtype_fuse_concat: Set[str] = set()
        self.phtype_table: Set[str] = set()
        self.phtype_ph: Set[str] = set()

        # 解析 meta 文件中保存的转换信息，辅助判断是否可以做 refit
        self.engine_refittable: bool = True
        self.engine_batch_size: List[int] = list()
        self.engine_predict_targets: List[str] = list()
        self.engine_cpu_targets: List[str] = list()
        self.engine_opset_version: int = OPSET_VERSION_DEFAULT
        self.engine_precision: str = ENGINE_PRECISION

    def parse_slice(self):
        model_meta_file_path = os.path.join(self.model_path, 'model.meta')
        with open(model_meta_file_path, 'r') as f:
            model_meta_json = json.load(f)

        self.model_version = model_meta_json['modelVersion']
        self.model_format = model_meta_json['modelFormat']
        self.model_format_version = model_meta_json['modelFormatVersion']

        for slice_def_json in model_meta_json['modelSliceDef']:
            slice_name = slice_def_json['name']
            slice_type = slice_def_json['type']
            slice_def = SliceDef(slice_name, slice_type)
            for file_desc_json in slice_def_json['files']:
                file_location = file_desc_json['location']
                slice_def.add_file(file_location)
            slice_def.parse_files()
            self.model_slice[slice_name] = slice_def

            if slice_type == 'dense':
                for deps_json in slice_def_json['deps']:
                    deps_version = deps_json['version']
                    self.deps.append(deps_version)

        # 解析转换组件信息
        if 'modelConvert' in model_meta_json.keys():
            convert_dict = model_meta_json['modelConvert']
            dst_model_config = convert_dict["dstModelConfig"]
            self.engine_refittable = bool(dst_model_config["engineRefittable"])
            self.engine_batch_size = [int(i) for i in re.split(';|,', dst_model_config.get("engineBatchSize", ""))]
            self.engine_predict_targets = re.split(';|,', dst_model_config.get("enginePredictTargets", ""))
            self.engine_cpu_targets = re.split(';|,', dst_model_config.get("engineCpuTargets", ""))
            self.engine_opset_version = int(dst_model_config.get("engineOpsetVersion", str(OPSET_VERSION_DEFAULT)))
            self.engine_precision = dst_model_config.get("enginePrecision", ENGINE_PRECISION)

    @staticmethod
    def _parse_kv(text: str):
        result = dict()
        for pair_str in text.strip().split():
            pair_tuple = pair_str.split('=', maxsplit=1)
            if len(pair_tuple) == 1:
                k = pair_tuple[0]
                result[k] = ''
            else:
                k, v = pair_tuple
                result[k] = v
        return result

    def _parse_node_with_default(self, xml: etree.ElementBase, path: str):
        target_nodes = xml.xpath(path)
        if len(target_nodes) == 0:
            return None, {}
        else:
            node = target_nodes[0]
            return node, self._parse_kv(node.text)

    def parse_dense_parameters_conf(self, slice_name: str = 'dense'):
        if self.conf_parsed:
            return
        self.conf_parsed = True

        conf_file = os.path.join(self.model_path, self.model_slice[slice_name].conf_file)
        try:
            with open(conf_file, 'r') as f:
                conf_file_contents = f.read()
        except UnicodeDecodeError:
            with gzip.open(conf_file, 'rb') as f:
                conf_file_contents = f.read()
        xml = etree.XML(conf_file_contents)

        # read parameter count
        dense_parameter_node, dense_parameter_node_dict = self._parse_node_with_default(xml, '/model/parameter/dense')
        param_count = int(dense_parameter_node_dict.get('param_num', '0'))

        if slice_name not in self.parameters:
            self.parameters[slice_name] = dict()

        # read parameter
        for i in range(param_count):
            param_node = dense_parameter_node.xpath('param_%d' % i)[0]
            param_node_dict = self._parse_kv(param_node.text)
            param_id = param_node_dict['param_id']
            if 'shape' in param_node_dict and param_node_dict['shape'] != '':
                param_shape = [int(dim) for dim in param_node_dict['shape'].split(',')]
            else:
                param_shape = None

            param_def = ParameterDef(param_node_dict['model_slice'], param_id, param_node_dict['param_name'],
                                     param_node_dict['row_num'], param_node_dict['col_num'], param_shape,
                                     param_node_dict['var_ph_name'], param_node_dict['var_update_op'])

            self.parameters[slice_name][param_id] = param_def

        # read control placeholder count
        control_placeholder_node, control_placeholder_node_dict = \
            self._parse_node_with_default(xml, '/model/control_placeholder')
        cph_count = int(control_placeholder_node_dict.get('cph_num', '0'))

        # read control placeholder
        if slice_name not in self.control_placeholders:
            self.control_placeholders[slice_name] = dict()

        for i in range(cph_count):
            cph_node = control_placeholder_node.xpath('cph_%d' % i)[0]
            cph_node_dict = self._parse_kv(cph_node.text)
            cph_name = cph_node_dict['cph_name']
            cph_def = ControlPlaceholderDef(cph_name, cph_node_dict['cph_type'], cph_node_dict['inference_value'])
            self.control_placeholders[slice_name][cph_name] = cph_def

        # read predict target
        predict_target_node = xml.xpath('/model/graph')[0]
        predict_target_node_dict = self._parse_kv(predict_target_node.text)
        if "custom_warmup_list" in predict_target_node_dict:
            self.predict_target = [target.strip() for target in predict_target_node_dict['custom_warmup_list'].split(',')]

        # read dense placeholder
        dense_placeholder_node, dense_placeholder_node_dict = \
            self._parse_node_with_default(xml, '/model/placeholder/dense')
        dense_placeholder_count = int(dense_placeholder_node_dict.get('ph_num', '0'))
        for i in range(dense_placeholder_count):
            dph_node = dense_placeholder_node.xpath('ph_%d' % i)[0]
            dph_node_dict = self._parse_kv(dph_node.text)
            dph_name = dph_node_dict['ph_name']
            dph_size = int(dph_node_dict['upper_bound']) - int(dph_node_dict['lower_bound']) + 1
            self.dense_placeholders[dph_name] = DensePlaceholderDef(dph_name, dph_size)

        # read sparse placeholder
        sparse_placeholder_node, sparse_placeholder_node_dict = \
            self._parse_node_with_default(xml, '/model/placeholder/sparse')
        sparse_placeholder_count = int(sparse_placeholder_node_dict.get('ph_num', '0'))
        for i in range(sparse_placeholder_count):
            sph_node = sparse_placeholder_node.xpath('ph_%d' % i)[0]
            sph_node_dict = self._parse_kv(sph_node.text)
            table_name = sph_node_dict['embedding_table']
            is_sparse = bool(int(sph_node_dict.get('is_ph_sparse', '0')))
            ph_name = sph_node_dict['ph_name']
            self.sparse_placeholders[ph_name] = SparsePlaceholderDef(table_name, is_sparse, ph_name)

            # mark dynamic node
            if 'fuse_output_name' in sph_node_dict.keys() and sph_node_dict['fuse_combine_method'] == 'concat':
                self.phtype_fuse_concat.add(sph_node_dict['fuse_output_name'])

            if 'fuse_output_name' not in sph_node_dict.keys():
                self.phtype_table.add(table_name)
                self.phtype_ph.add(ph_name)

    def _read_dense_parameter_partition(self, slice_name: str, param_id: str,
                                        partition: ParameterPartition, f_data: BinaryIO):
        f_data.read(struct.calcsize('l'))
        row_size = partition.row_end - partition.row_begin + 1
        col_size = partition.col_end - partition.col_begin + 1
        size = row_size * col_size
        bin_data = f_data.read(size * struct.calcsize('f'))
        parameter_array = np.reshape(np.frombuffer(bin_data, np.float32), (row_size, col_size))
        self.parameters[slice_name][param_id].set_parameter(partition, parameter_array)

    def read_dense_parameters(self, slice_name: str = 'dense'):
        if self.param_parsed:
            return
        if not self.conf_parsed:
            raise Exception('Conf file not parsed')
        self.param_parsed = True

        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(self.parse_dense_parameters, k, meta_file, slice_name)
                for k, meta_file in self.model_slice[slice_name].meta_files.items()]
            for future in as_completed(futures):
                try:
                    result = future.result()
                    logging.info('Task completed with result: %s' % result)
                except Exception as e:  # pylint: disable=broad-except
                    logging.error('Task failed with exception: %s' % e)

        # check all parameters have been set
        for param_id, param_def in self.parameters[slice_name].items():
            if not param_def.check_parameter_all_set():
                logging.warning('Parameter %s not set, name: %s' % (param_id, param_def.param_name))

    def parse_dense_parameters(self, k: str, meta_file: str, slice_name: str = 'dense'):
        # parse dense parameters meta and read parameters
        with open(os.path.join(self.model_path, meta_file), 'r') as f:
            xml = etree.XML(f.read())
        data_file = self.model_slice[slice_name].data_files[k]
        with open(os.path.join(self.model_path, data_file), 'rb') as f_data:
            _, sparse_node_dict = self._parse_node_with_default(xml, '/model/sparse')
            sparse_param_count = int(sparse_node_dict.get('param_num', '0'))
            assert sparse_param_count == 0, 'Sparse parameter not supported'

            dense_node, dense_node_dict = self._parse_node_with_default(xml, '/model/dense')
            dense_param_count = int(dense_node_dict.get('param_num', '0'))
            logging.info('Parse dense parameters meta: %s' % k)
            for i in range(dense_param_count):
                dense_param_node = dense_node.xpath('param_%d' % i)[0]
                desc_node_dict = self._parse_kv(dense_param_node.xpath('description')[0].text)
                param_id = desc_node_dict['param_id']
                storage_node = dense_param_node.xpath('storage')[0]
                storage_node_dict = self._parse_kv(storage_node.text)
                partition_count = int(storage_node_dict['partition_num'])
                for j in range(partition_count):
                    partition_node_dict = self._parse_kv(storage_node.xpath('partition_%d' % j)[0].text)
                    partition_def = ParameterPartition(int(partition_node_dict['row_begin']),
                                                       int(partition_node_dict['row_end']),
                                                       int(partition_node_dict['col_begin']),
                                                       int(partition_node_dict['col_end']))
                    self._read_dense_parameter_partition(slice_name, param_id, partition_def, f_data)
        return k
