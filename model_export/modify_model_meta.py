import json
import pynvml
import tensorrt as trt
import pycuda.driver as cuda

from typing import List
from components import const

def _get_device_type():
    # 获取显卡类型
    pynvml.nvmlInit()
    gpu_id = 0
    handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_id)
    gpu_device_info = pynvml.nvmlDeviceGetName(handle)
    pynvml.nvmlShutdown()
    return gpu_device_info

def _get_cuda_version():
    cuda.init()
    version = cuda.get_version()
    return '.'.join(str(i) for i in version)

def modify_model_meta_file(meta_file_path: str, engine_files: List[str], batch_size: list, cpu_targets: list,
                           engine_type: str, version: str, job_id: int, session_id: str, appinstance_name: str,
                           opset_version: int, pridect_targets: list):
    with open(meta_file_path, 'r') as f:
        meta_dict = json.load(f)

    # 更新app_instance_id
    meta_dict['app_instance_id'] = appinstance_name
    # 原始无量模型为：{'modelFormat':'numerous'}
    model_format = meta_dict['modelFormat']
    if engine_type == 'trt':
        meta_dict['modelFormat'] = model_format + '_trt'
    else:
        meta_dict['modelFormat'] = model_format + '_evart'

    has_dense_slice = False
    for slice in meta_dict['modelSliceDef']:
        if slice['name'] == 'dense':
            has_dense_slice = True
            for engine_file in engine_files:
                slice['files'].append({
                    'location': engine_file,
                    'type': 'variableData'
                })
            break

    assert has_dense_slice, 'Dense slice not found'

    # 组件转换信息 
    convert_dict = dict()
    convert_dict["modelConverterName"] = "NumerousTensorRT"
    convert_dict["modelConverterCode"] = const.VENUS_OP_CODE
    convert_dict["modelConverterVersion"] = const.VENUS_OP_VERSION
    convert_dict["modelServableType"] = "numerous"
    convert_dict["modelJobId"] = job_id
    convert_dict["modelSessionId"] = session_id

    # 获取环境信息
    gpu_device_info = _get_device_type()
    try:
        device_info = gpu_device_info.decode()
    except Exception as e:
        device_info = gpu_device_info
    cuda_version = _get_cuda_version()
    trt_version = trt.__version__

    convert_env = {
        "deviceInfo" : device_info,
        "cudaVersion" : cuda_version,
        "tensorrtVersion" : trt_version
    }
    convert_dict["convertEnv"] = convert_env

    # 获取原始模型信息
    ori_model_config = {
        "modelName" : const.ORI_MODEL_NAME,
        "modelVersion" : version
    }
    convert_dict["oriModelConfig"] = ori_model_config

    # 获取转换模型信息
    dst_model_config = {
        "engineType" : engine_type,
        "engineVersion" : const.ENGINE_VERSION,
        "engineBatchSize" : ','.join(str(v) for v in batch_size),
        "enginePrecision" : const.ENGINE_PRECISION,
        "engineRefittable" : const.USE_REFIT,
        "engineCpuTargets" : "",
        "enginePredictTargets" : "",
        "engineOpsetVersion" : opset_version,
    }
    if cpu_targets is not None:
        dst_model_config["engineCpuTargets"] = ','.join(str(v) for v in cpu_targets)

    if pridect_targets is not None:
        dst_model_config["enginePredictTargets"] = ','.join(str(v) for v in pridect_targets)

    convert_dict["dstModelConfig"] = dst_model_config

    meta_dict['modelConvert'] = convert_dict

    with open(meta_file_path, 'w') as f:
        f.write(json.dumps(meta_dict, indent=4))

