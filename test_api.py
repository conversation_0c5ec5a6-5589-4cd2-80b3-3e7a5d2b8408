import json
import os
import time
import sys
from venus_api_base.http_client import HttpClient
from venus_api_base.config import Config
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client

# user
mysecret_id = ''
mysecret_key = ''


http_client = HttpClient(secret_id=mysecret_id, secret_key=mysecret_key, config=Config())

def CosDown(ret):
    srcKey = 'numerous_v2/default/test_offline/2022_10_09_164649/graph.pb'

    msg = ret['data']
    bucket = msg['bucketName']
    secret_id = msg['accessKey']
    secret_key = msg['secretKey']
    region = msg['region']
    endpoint = 'cos-internal.%s.tencentcos.cn' % region
    token = None
    if msg['owner'] == "venus":
        token = msg['token']
    scheme = 'http'

    config = CosConfig(Endpoint=endpoint, Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
    client = CosS3Client(config)
    response = client.get_object(
        Bucket=bucket,
        Key=srcKey
    )
    response['Body'].get_stream_to_file('./graph.pb')   

def CosCopy(ret):
    srcKey = 'numerous_v2/default/qqmusic_gpu_0928_v1/2022_10_09_164649/graph.pb'
    destKey = 'numerous_v2/default/qqmusic_gpu_0928_v1_cosmp_test/2022_10_09_164649/graph.pb'

    msg = ret['data']['destTempStorageInfo']
    bucket = msg['bucketName']
    secret_id = msg['accessKey']
    secret_key = msg['secretKey']
    region = msg['region']
    endpoint = 'cos-internal.%s.tencentcos.cn' % region
    token = None
    if msg['owner'] == "venus":
        token = msg['token']
    scheme = 'http'

    config = CosConfig(Endpoint=endpoint, Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
    client = CosS3Client(config)

    response = client.copy(
        Bucket=bucket,
        Key=destKey,
        CopySource={
            'Bucket': bucket,
            'Key': srcKey,
            'Region': region,
            'Endpoint': endpoint
        }
    )

def ModelInfoGet(model_name):
    url = 'http://v2.open.venus.oa.com/model/info/get?modelName=%s' \
          % (model_name)
    ret = http_client.get(url, header={})


def ModelInstanceInfoGet(model_name, model_version):
    url = 'http://v2.open.venus.oa.com/model/instance/info/get?modelName=%s&modelVersion=%s' \
          % (model_name, model_version)
    ret = http_client.get(url, header={})


def ModelTempStorageInfo(model_name, model_version):
    # 获取临时密钥
    url = 'http://v2.open.venus.oa.com/model/temp/storage/info?modelName=%s&modelVersion=%s' \
          % (model_name, model_version)
    ret = http_client.get(url, header={})

    CosDown(ret)


def ModelCopyTempStorageInfo(model_name, model_version, model_name_dest):
    url = 'http://v2.open.venus.oa.com/model/copy/temp/storage/info?destModelName=%s&destModelVersion=%s&srcModelName=%s&srcModelVersion=%s' \
          % (model_name_dest, model_version, model_name, model_version)
    ret = http_client.get(url, header={})

    upload_time_s = time.time()
    CosCopy(ret)
    upload_time_e = time.time()
    print('Upload model succeed, time cost: %.3f seconds' % (upload_time_e - upload_time_s)) 


def ModelSave():
    model_name = 'qqmusic_gpu_0928_v1_cosmp_test4'

    body_dict = {
        'modelName': model_name,
        'modelTrainFrame': 'numerous',
        'modelTrainAlg': 'rtml',
        'modelTrainType': 'online',
        'appGroupId': '874',
        'jobId': '-1',
        'maintainer': 'ttsybyweng'
    }
    header = {'Content-Type': 'application/json'}
    body = json.dumps(body_dict)

    ret = http_client.post('http://v2.open.venus.oa.com/model/save?ModelRegisterDto=', header=header, body=body)

if __name__ == '__main__':

    model_name = 'test_offline'
    model_version = '2022_10_09_164649'
    out_model_name = 'qqmusic_gpu_0928_v1_cosmp_test'

    ModelInfoGet(model_name)

    ModelInstanceInfoGet(model_name, model_version)

    ModelTempStorageInfo(model_name, model_version)

    ModelCopyTempStorageInfo(model_name, model_version, out_model_name)
    
    ModelSave()



